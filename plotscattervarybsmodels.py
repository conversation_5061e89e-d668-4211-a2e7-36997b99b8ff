import pandas
import matplotlib.pyplot as plt
import seaborn as sns


def plotfic(x, y):
    fig, axes = plt.subplots(figsize=(5, 2.2))
    ax2 = axes
    sns.scatterplot(x=x,
                    y=y,
                    s=10,
                    alpha=1,
                    legend=None,
                    color=['#002c53'],
                    ec='#002c53',
                    ax=ax2)
    ax2.set_ylabel('Exec Time (ms)')
    ax2.set_xlabel('GFLOPs')
    ax2.set_xscale('log')
    ax2.set_yscale('log')
    ax2.grid(color='#DCDCDC')

    plt.savefig('./picture/figure_3.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_3.pdf', bbox_inches='tight')
    plt.close()


df = pandas.read_csv("./data/bstimehug.csv")
cols = ['ops', 'bs=512', 'bs=1', 'bs=2', 'bs=4', 'bs=8', 'bs=16', 'bs=32', 'bs=64', 'bs=128', 'bs=256']

df = df.drop(df[(df['type'] == 'outliers')].index)

df = df.drop(df[(df['id'] >= 602)].index)
df['ops'] = df['ops'] / 1e9
df['ops=512'] = df['ops'] * 512
df['ops=1'] = df['ops']
df['ops=2'] = df['ops'] * 2
df['ops=4'] = df['ops'] * 4
df['ops=8'] = df['ops'] * 8
df['ops=16'] = df['ops'] * 16
df['ops=32'] = df['ops'] * 32
df['ops=64'] = df['ops'] * 64
df['ops=128'] = df['ops'] * 128
df['ops=256'] = df['ops'] * 256

result512 = pandas.concat([df['ops=512'], df['bs=512']], axis=1)
result1 = pandas.concat([df['ops=1'], df['bs=1']], axis=1)
result2 = pandas.concat([df['ops=2'], df['bs=2']], axis=1)
result4 = pandas.concat([df['ops=4'], df['bs=4']], axis=1)
result8 = pandas.concat([df['ops=8'], df['bs=8']], axis=1)
result16 = pandas.concat([df['ops=16'], df['bs=16']], axis=1)
result32 = pandas.concat([df['ops=32'], df['bs=32']], axis=1)
result64 = pandas.concat([df['ops=64'], df['bs=64']], axis=1)
result128 = pandas.concat([df['ops=128'], df['bs=128']], axis=1)
result256 = pandas.concat([df['ops=256'], df['bs=256']], axis=1)
result1.columns = ['ops', 'time']
result2.columns = ['ops', 'time']
result4.columns = ['ops', 'time']
result8.columns = ['ops', 'time']
result16.columns = ['ops', 'time']
result32.columns = ['ops', 'time']
result64.columns = ['ops', 'time']
result128.columns = ['ops', 'time']
result256.columns = ['ops', 'time']
result512.columns = ['ops', 'time']

result = pandas.concat([result4, result8, result16, result32, result64, result128, result256, result512])
plotfic(result['ops'], result['time'])
print('All the figures are in: ./picture/')
print(
    'Figure 3: The execution times of all the networks in our dataset against their FLOPs, when batch size is 4 or higher. The execution times of DNN networks are generally linearly correlated to FLOPs, with exceptions when the operation count is small.')
print('Filename: figure_3')
