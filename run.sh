#!/usr/bin/env bash

# 深度神经网络执行时间预测 - 主运行脚本
#
# 该脚本用于运行完整的性能分析和图表生成流程
# 适用于Case 1: 基于已有GPU数据集进行预测分析
#
# 运行前提：
# 1. 已安装所有依赖包（运行install.sh）
# 2. data/目录下有完整的GPU性能数据
# 3. picture/目录存在（用于保存生成的图表）
#
# 执行步骤：
# 1. 生成基础性能分析图表（FLOPs vs 执行时间等）
# 2. 生成论文中的详细分析图表
# 3. 进行回归分析并计算误差率
#
# 输出：
# - 18个性能分析图表（PNG和PDF格式）
# - 各种模型的误差率统计
#
# 使用方法：
# chmod +x run.sh
# ./run.sh

echo "开始执行深度神经网络性能分析..."

echo "步骤1: 生成基础性能分析图表..."
python plotscattervarybsmodels.py

echo "步骤2: 生成详细分析图表..."
python picturepart.py

echo "步骤3: 进行回归分析..."
python regression.py

echo "分析完成！所有图表已保存到 ./picture/ 目录"