import pandas
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns


def bs512resnetvgg():
    df = pandas.read_csv('./data/paper/bs1-512torchvision.csv',
                         usecols=['model', 'id', 'timebs1', 'timebs512', 'ops', 'group'])

    df = df.drop(df[(df['group'] == 1)].index)
    df = df.drop(df[(df['group'] == 2)].index)
    df = df.drop(df[(df['group'] == 4)].index)
    fig_data = df.copy()
    fig_data.loc[:, 'ops'] = fig_data['ops'] / 1e9
    fig, axes = plt.subplots(figsize=(5, 2))
    ax1 = axes
    sns.scatterplot(data=fig_data,
                    x='ops', y='timebs512', hue='group', style='group',
                    s=80,
                    alpha=1,
                    palette=['#002c53', '#f74d4d'],
                    ax=ax1)

    ax1.set_xlabel('GFLOPs')
    ax1.set_ylabel('Exec Time (ms)')
    ax1.set_yticks([0, 100, 200, 300, 400])
    handles, labels = ax1.get_legend_handles_labels()
    handles[0]._sizes = [70]
    handles[1]._sizes = [70]
    labels = ['ResNet', 'VGG']
    ax1.legend(handles, labels,
               frameon=False,
               fancybox=None,
               bbox_to_anchor=(.5, 1.23), loc=9, ncol=2)
    plt.grid(color='#DCDCDC')
    plt.savefig('./picture/figure_4.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_4.pdf', bbox_inches='tight')
    plt.close()


def timevarybs():
    df = pandas.read_csv('./data/paper/timeforresvggmobile.csv',
                         usecols=['bs', 'resnet50', 'mobilenetv2', 'vgg11', 'vgg16',
                                  'resnetperimage', 'mobilenetv2perimage', 'vgg11perimage', 'vgg16perimage'])
    df = df.drop(df.tail(215).index)
    df = df[[i % 2 == 0 for i in range(len(df.index))]]
    df = df[[i % 2 == 0 for i in range(len(df.index))]]
    fig_data = df.copy()
    fig, axes = plt.subplots(figsize=(5, 2))
    ax1 = axes
    sns.scatterplot(data=fig_data,
                    x='bs', y='resnet50', label='ResNet-50',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    ax=ax1)
    sns.scatterplot(data=fig_data,
                    x='bs', y='mobilenetv2', marker='s', label='MobileNetV2',
                    s=50,
                    alpha=1,
                    color=['#f74d4d'],
                    ax=ax1)
    sns.scatterplot(data=fig_data,
                    x='bs', y='vgg16', marker='P', label='VGG-16',
                    s=50,
                    alpha=1,
                    color=['#ffa510'],
                    ax=ax1)

    ax1.set_xlabel('Batch Size')
    ax1.set_ylabel('Exec Time (ms)')
    ax1.set_xticks([2, 10, 18, 26, 34, 42, 50, 58, 66, 74, 82])
    ax1.set_yticks([0, 10, 20, 30, 40])
    handles, labels = ax1.get_legend_handles_labels()
    handles[0]._sizes = [70]
    handles[1]._sizes = [70]
    handles[2]._sizes = [70]
    ax1.legend(handles, labels,
               frameon=False,
               fancybox=None,
               bbox_to_anchor=(.5, 1.23), loc=9, ncol=4)
    plt.grid(color='#DCDCDC')
    plt.savefig('./picture/figure_5.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_5.pdf', bbox_inches='tight')
    plt.close()


def flopsvarybs():
    df = pandas.read_csv('./data/paper/flopsforresvggmobile.csv',
                         usecols=['bs', 'resnet50', 'mobilenetv2', 'vgg16'])
    df = df.drop(df.tail(1).index)
    fig_data = df.copy()
    fig, axes = plt.subplots(figsize=(5, 2))
    ax1 = axes
    sns.scatterplot(data=fig_data,
                    x='bs', y='resnet50', label='ResNet-50',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    ax=ax1)
    sns.scatterplot(data=fig_data,
                    x='bs', y='mobilenetv2', marker='s', label='MobileNetV2',
                    s=50,
                    alpha=1,
                    color=['#f74d4d'],
                    ax=ax1)
    sns.scatterplot(data=fig_data,
                    x='bs', y='vgg16', marker='P', label='VGG-16',
                    s=50,
                    alpha=1,
                    color=['#ffa510'],
                    ax=ax1)

    ax1.set_xlabel('Batch Size')
    ax1.set_ylabel('TFLOPS')
    ax1.set_xticks([8, 64, 128, 192, 256, 320, 384, 448, 512])
    ax1.set_yticks([0, 2000, 4000, 6000, 8000, 10000])
    ax1.set_yticklabels([0, 2, 4, 6, 8, 10])
    handles, labels = ax1.get_legend_handles_labels()
    handles[0]._sizes = [70]
    handles[1]._sizes = [70]
    handles[2]._sizes = [70]
    ax1.legend(handles, labels,
               frameon=False,
               fancybox=None,
               bbox_to_anchor=(.5, 1.23), loc=9, ncol=3)
    plt.grid(color='#DCDCDC')
    plt.savefig('./picture/figure_6.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_6.pdf', bbox_inches='tight')
    plt.close()


def scatteroflayer():
    df = pandas.read_csv('./data/paper/layertime.csv')
    dftest = df[(df['cpueventname'] == 'aten::batch_norm')]
    dftest = pandas.concat([dftest, df[(df['cpueventname'] == 'aten::conv2d')]], ignore_index=True)
    dftest = pandas.concat([dftest, df[(df['cpueventname'] == 'aten::linear')]], ignore_index=True)
    dftest = pandas.concat([dftest, df[(df['cpueventname'] == 'aten::adaptive_avg_pool2d')]], ignore_index=True)
    fig_data = dftest.copy()
    fig_data.loc[:, 'kerneltime'] = fig_data['kerneltime'] / 1000
    fig_data.loc[:, 'ops'] = fig_data['ops'] / 1e9

    fig, axes = plt.subplots(figsize=(5, 1.8))
    ax2 = axes
    sns.scatterplot(data=fig_data,
                    x='ops',
                    y='kerneltime',
                    s=70,
                    alpha=1, hue='cpueventname', style='cpueventname',
                    # legend=None,
                    palette=['#0c84c6', '#002c53', '#ffa510', '#f74d4d'],
                    # ec='#002c53',
                    markers=['P', 'd', 'X', '^'],
                    ax=ax2)

    ax2.set_ylabel('Exec time (ms)')
    ax2.set_xlabel('GFLOPs')
    ax2.set_xscale('log')
    ax2.set_yscale('log')
    ax2.grid(color='#DCDCDC')
    ax2.set_yticks([0.1, 10, 1000])
    handles, labels = ax2.get_legend_handles_labels()
    handles[0]._sizes = [70]
    handles[1]._sizes = [70]
    handles[2]._sizes = [70]
    labels = ['BN', 'CONV', 'FC', 'Pooling']
    ax2.legend(handles, labels,
               frameon=False,
               fancybox=None,
               bbox_to_anchor=(.5, 1.23), loc=9, ncol=4)

    plt.savefig('./picture/figure_7.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_7.pdf', bbox_inches='tight')
    plt.close()


def convpatternv3():
    df = pandas.read_csv('./data/paper/conv2d.csv',
                         usecols=['modelid', 'cpueventname', 'inputproducts', 'kernelduration', 'warpsperSM',
                                  'kernelname', 'outputproducts', 'ops', 'block'])

    dfops = df[
        df['kernelname'].str.startswith('  void at::native::(anonymous namespace)::conv_depthwise2d_forward_kernel')]
    dfinput = df[
        df['kernelname'].str.startswith('  void cudnn::ops::nchwToNhwcKernel<float; float; float; true; true;')]
    dfoutput = df[
        df['kernelname'].str.startswith('  void cudnn::ops::nhwcToNchwKernel<float; float; float; true; true;')]

    dfops.loc[:, 'kernelduration'] = dfops['kernelduration'] / 1000
    dfops.loc[:, 'ops'] = dfops['ops'] / 1e9

    dfinput.loc[:, 'kernelduration'] = dfinput['kernelduration'] / 1000
    dfinput.loc[:, 'ops'] = dfinput['ops'] / 1e9

    dfoutput.loc[:, 'kernelduration'] = dfoutput['kernelduration'] / 1000
    dfoutput.loc[:, 'ops'] = dfoutput['ops'] / 1e9

    fig, axes = plt.subplots(3, 3, figsize=(9, 7))
    plt.subplots_adjust(left=0.12, right=0.98, bottom=0.09, top=0.92, hspace=0.38, wspace=0.18)
    ax1 = axes[1, 1]
    sns.scatterplot(data=dfops,
                    x='ops', y='kernelduration',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    legend=None,
                    ax=ax1)

    ax1.set_xlabel('GFLOPs')
    ax1.set_ylabel('')
    ax1.set_yticks([0, 5, 10, 15, 20, 25, ])
    ax1.grid(color='#DCDCDC')

    ax12 = axes[1, 2]
    sns.scatterplot(data=dfops,
                    x='outputproducts', y='kernelduration',
                    s=50,
                    alpha=1,
                    legend=None,
                    color=['#002c53'],
                    ax=ax12)

    ax12.set_xlabel('Output N x C x H x W')
    ax12.set_ylabel('')
    ax12.set_yticks([0, 5, 10, 15, 20, 25, ])
    ax12.set_xticks([0, 0.2e9, 0.4e9, 0.6e9, 0.8e9])
    ax12.set_xticklabels(['0', '.2G', '.4G', '.6G', '.8G'])
    ax12.grid(color='#DCDCDC')

    ax13 = axes[1, 0]
    sns.scatterplot(data=dfops,
                    x='inputproducts', y='kernelduration',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    ax=ax13)

    ax13.set_xlabel('Input N x C x H x W')
    ax13.set_ylabel('Exec Time (ms)')
    ax13.set_xticks([0, 0.2e9, 0.4e9, 0.6e9, 0.8e9])
    ax13.set_xticklabels(['0', '.2G', '.4G', '.6G', '.8G'])
    ax13.set_yticks([0, 5, 10, 15, 20, 25, ])
    ax13.grid(color='#DCDCDC')

    ax20 = axes[2, 1]
    sns.scatterplot(data=dfoutput,
                    x='ops', y='kernelduration',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    legend=None,
                    ax=ax20)

    ax20.set_xlabel('GFLOPs')
    ax20.set_ylabel('')
    ax20.set_xticks([0, 100, 200, 300, 400])
    ax20.grid(color='#DCDCDC')

    ax21 = axes[2, 0]
    sns.scatterplot(data=dfoutput,
                    x='inputproducts', y='kernelduration',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    ax=ax21)

    ax21.set_xlabel('Input N x C x H x W')
    ax21.set_ylabel('Exec Time (ms)')
    ax21.set_xticks([0, 0.4e9, 0.8e9, 1.2e9, 1.6e9])
    ax21.set_xticklabels([0, '.4G', '.8G', '1.2G', '1.6G'])
    ax21.grid(color='#DCDCDC')

    ax2 = axes[2, 2]
    sns.scatterplot(data=dfoutput,
                    x='outputproducts', y='kernelduration',
                    s=50,
                    alpha=1,
                    legend=None,
                    color=['#002c53'],
                    ax=ax2)

    ax2.set_xlabel('Output N x C x H x W')
    ax2.set_ylabel('')
    ax2.set_xticks([0, 0.1e9, 0.2e9, 0.3e9, 0.4e9])
    ax2.set_xticklabels(['0', '.1G', '.2G', '.3G', '.4G'])
    ax2.grid(color='#DCDCDC')

    ax30 = axes[0, 1]
    sns.scatterplot(data=dfinput,
                    x='ops', y='kernelduration',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    legend=None,
                    ax=ax30)

    ax30.set_xlabel('GFLOPs')
    ax30.set_ylabel('')
    ax30.set_xticks([0, 100, 200, 300, 400])
    ax30.grid(color='#DCDCDC')

    ax32 = axes[0, 2]
    sns.scatterplot(data=dfinput,
                    x='outputproducts', y='kernelduration',
                    s=50,
                    alpha=1,
                    legend=None,
                    color=['#002c53'],
                    ax=ax32)

    ax32.set_xlabel('Output N x C x H x W')
    ax32.set_ylabel('')
    ax32.set_xticks([0, 0.1e9, 0.2e9, 0.3e9, 0.4e9])
    ax32.set_xticklabels(['0', '.1G', '.2G', '.3G', '.4G'])
    ax32.grid(color='#DCDCDC')

    ax3 = axes[0, 0]
    sns.scatterplot(data=dfinput,
                    x='inputproducts', y='kernelduration',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    ax=ax3)

    ax3.set_xlabel('Input N x C x H x W')
    ax3.set_ylabel('Exec Time (ms)')
    ax3.set_xticks([0, 0.4e9, 0.8e9, 1.2e9, 1.6e9])
    ax3.set_xticklabels([0, '.4G', '.8G', '1.2G', '1.6G'])
    ax3.grid(color='#DCDCDC')
    plt.savefig('./picture/figure_8.png', dpi=600)
    plt.savefig('./picture/figure_8.pdf')
    plt.close()


def bwflopsratio():
    data = {'GPUs': ['A40', 'A100', '1080 Ti', 'TITAN', 'RTX A5000', 'Quadro P620'],
            'BW Efficiency': [10.1084282, 10.58230664, 8.853715335, 9.60220097, 11.58333984, 8.178964529],
            'Compute Efficiency': [18.02270146, 10.11671955, 36.22335425, 37.92424163, 30.70788745, 45.32742404]}
    df = pd.DataFrame(data)
    stacked_data = df
    stacked_data = stacked_data.melt(id_vars=['GPUs'], value_vars=['BW Efficiency', 'Compute Efficiency'])
    stacked_data.columns = ['GPUs', 'Configuration', 'Efficiency']
    stacked_data = stacked_data.reset_index()

    fig, ax = plt.subplots(figsize=(5, 2.2))
    sns.barplot(data=stacked_data,
                x='GPUs', hue='Configuration', y='Efficiency',
                width=0.7, palette=['#002c53', '#f74d4d'],
                ax=ax)

    handles, labels = ax.get_legend_handles_labels()
    ax.legend(handles, labels,
              frameon=False,
              fancybox=None,
              facecolor=None, edgecolor=None,
              bbox_to_anchor=(0.5, 1.18), loc=9, ncol=2)

    ax.set_yticks([0, 10, 20, 30, 40])
    ax.set_yticklabels(['0', '10%', '20%', '30%', '40%'])
    ax.set_xlabel('')
    plt.grid(color='#DCDCDC')
    plt.xticks(rotation=10)
    plt.savefig('./picture/figure_9.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_9.pdf', bbox_inches='tight')
    plt.close()


def casestudy2_2():
    data = {'Bandwidth (GB/s)': [1400, 1300, 1200, 1100, 1000, 900, 800, 700, 672, 600, 500, 400, 300, 200, 100],
            'Densenet169': [220.8372061, 237.1251901, 256.029858, 278.2436985, 304.7302544, 336.8730497, 376.7364799,
                            427.552857, 444.3987403, 494.7086811, 587.9700432, 727.3306258, 962.4472175, 1471.862399,
                            4621.391235],
            'Resnet50': [64.94389464, 69.71949587, 75.25512261, 81.74820217, 89.4711087, 98.81049667, 110.3343411,
                         124.9125161, 129.7138726, 143.9485461, 169.8641404, 207.2308924, 265.8527596, 371.3531925,
                         619.9432672]}
    df = pd.DataFrame(data)
    fig_data = df.copy()
    fig, axes = plt.subplots(figsize=(5, 2))
    ax1 = axes
    sns.scatterplot(data=fig_data,
                    x='Bandwidth (GB/s)', y='Resnet50',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    ax=ax1)

    ax1.set_xlabel('Bandwidth (GB/s)')
    ax1.set_ylabel('Predicted Time (ms)')
    ax1.set_xticks([0, 200, 400, 600, 800, 1000, 1200, 1400])

    plt.scatter(x=672, y=129.7138726, color='#f74d4d')
    plt.axvline(x=672, color='#f74d4d', linestyle='--')
    plt.grid(color='#DCDCDC')
    plt.savefig('./picture/figure_15.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_15.pdf', bbox_inches='tight')
    plt.close()


def casestudy2():
    data = {'Bandwidth (GB/s)': [1400, 1300, 1200, 1100, 1000, 900, 800, 700, 672, 600, 500, 400, 300, 200, 100],
            'Densenet169': [220.8372061, 237.1251901, 256.029858, 278.2436985, 304.7302544, 336.8730497, 376.7364799,
                            427.552857, 444.3987403, 494.7086811, 587.9700432, 727.3306258, 962.4472175, 1471.862399,
                            4621.391235],
            'Resnet50': [64.94389464, 69.71949587, 75.25512261, 81.74820217, 89.4711087, 98.81049667, 110.3343411,
                         124.9125161, 129.7138726, 143.9485461, 169.8641404, 207.2308924, 265.8527596, 371.3531925,
                         619.9432672]}
    df = pd.DataFrame(data)
    fig_data = df.copy()
    fig, axes = plt.subplots(figsize=(5, 2))
    ax1 = axes
    sns.scatterplot(data=fig_data,
                    x='Bandwidth (GB/s)', y='Densenet169',
                    s=50,
                    alpha=1,
                    color=['#002c53'],
                    ax=ax1)

    ax1.set_xlabel('Bandwidth (GB/s)')
    ax1.set_ylabel('Predicted Time (ms)')
    ax1.set_xticks([0, 200, 400, 600, 800, 1000, 1200, 1400])
    ax1.set_yticks([0, 1000, 2000, 3000, 4000])
    plt.scatter(x=672, y=444.3987403, color='#f74d4d')
    plt.axvline(x=672, color='#f74d4d', linestyle='--')
    plt.grid(color='#DCDCDC')
    plt.savefig('./picture/figure_16.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_16.pdf', bbox_inches='tight')
    plt.close()


def casestudy2_8():
    data = {
        'model': ['ResNet-44', 'ResNet-50', 'ResNet-62', 'ResNet-77', 'DenseNet-161', 'DenseNet-169', 'DenseNet-121',
                  'DenseNet-201', 'ShuffleNet v1'],
        'A40-Measured': [100.878419, 108.2488805, 122.88255, 141.0064031, 278.0524597, 158.3654322, 130.032399,
                         203.6543447, 18.31461124],
        'TITAN-Measured': [114.1115878, 123.7581332, 143.3712097, 167.80233, 423.7357147, 229.0148234, 193.3197144,
                           282.8129079, 18.30661545],
        'A40-Predicted': [93.87651239, 99.7390844, 110.7443464, 124.8729658, 256.9740453, 151.966516, 126.0286189,
                          193.3074598, 18.75780356],
        'TITAN-Predicted': [111.1823631, 119.3315585, 135.8008961, 156.4162323, 410.4150445, 233.9091995, 197.5679574,
                            292.21361, 18.16879646],
        'test': [0, 0, 0, 0, 0, 0, 0, 0, 0],
        }
    df = pd.DataFrame(data)
    df = df.drop(df[(df['model'] == 'DenseNet-201')].index)
    df = df.drop(df[(df['model'] == 'ResNet-44')].index)
    df = df.drop(df[(df['model'] == 'ResNet-62')].index)
    stacked_data = df
    stacked_data = stacked_data.melt(id_vars=['model'],
                                     value_vars=['A40-Measured', 'A40-Predicted', 'TITAN-Measured', 'TITAN-Predicted'])
    stacked_data.columns = ['model', 'Configuration', 'Exec Time (ms)']
    stacked_data = stacked_data.reset_index()

    fig, ax = plt.subplots(figsize=(5, 2))
    sns.barplot(data=stacked_data,
                    x='model', hue='Configuration', y='Exec Time (ms)',
                    width=0.9, palette=['#002c53', '#8096a9', '#f74d4d', '#fba6a6'],
                    ax=ax)
    handles, labels = ax.get_legend_handles_labels()
    ax.legend(handles, labels,
              frameon=False,
              fancybox=None,
              facecolor=None, edgecolor=None,
              bbox_to_anchor=(0.5, 1.32), loc=9, ncol=2)

    ax.plot(-0.12, 130, 'X', color='#ffa510', markersize=5)
    ax.plot(0.88, 154, 'X', color='#ffa510', markersize=5)
    ax.plot(1.88, 286, 'X', color='#ffa510', markersize=5)
    ax.plot(2.88, 181, 'X', color='#ffa510', markersize=5)
    ax.plot(3.88, 156, 'X', color='#ffa510', markersize=5)
    ax.plot(5.3, 48, 'X', color='#ffa510', markersize=5)

    ax.set_yticks([0, 100, 200, 300, 400])
    ax.set_xlabel('')
    plt.xticks(rotation=15)
    plt.grid(color='#DCDCDC')
    plt.savefig('./picture/figure_18.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_18.pdf', bbox_inches='tight')
    plt.close()


def casestudy3_2():
    df = pandas.read_csv('./data/paper/casestudy2.csv',
                         usecols=['model', 'networkbw', 'normsimulatedtime'])
    df = df.drop(df[(df['networkbw'] == '8 GB/s')].index)
    df = df.drop(df[(df['networkbw'] == '1T GB/s')].index)
    df = df.drop(df[(df['networkbw'] == '2048 GB/s')].index)
    df = df.drop(df[(df['networkbw'] == '4096 GB/s')].index)
    df = df.drop(df[(df['networkbw'] == '8192 GB/s')].index)
    df = df.drop(df[(df['networkbw'] == '16384 GB/s')].index)

    df = df.drop(df[(df['model'] == 'DenseNet-169')].index)
    df = df.drop(df[(df['model'] == 'DenseNet-201')].index)
    df = df.drop(df[(df['model'] == 'ResNet-44')].index)
    df = df.drop(df[(df['model'] == 'ResNet-62')].index)

    stacked_data = df
    stacked_data = stacked_data.melt(id_vars=['model', 'networkbw'], value_vars=['normsimulatedtime'])
    stacked_data.columns = ['model', 'networkbw', 'variable', 'Speedup']
    stacked_data = stacked_data.reset_index()
    fig, ax = plt.subplots(figsize=(10, 3))
    sns.set(style="whitegrid", color_codes=True)
    colorlist = ['#fddbdb', '#fcb8b8', '#fa9494', '#f97171', '#f74d4d', '#631f1f']
    ax = sns.barplot(data=stacked_data,
                     x='model', hue='networkbw', y='Speedup', width=0.9,
                     palette=colorlist,
                     ec='k',
                     alpha=1,
                     ax=ax)

    handles, labels = ax.get_legend_handles_labels()
    ax.legend(handles, labels,
              frameon=False,
              fancybox=None,
              facecolor=None, edgecolor=None,
              bbox_to_anchor=(0.5, 1.35), loc=9, ncol=3, fontsize=17)
    plt.grid(color='#DCDCDC')
    plt.xticks(rotation=13, fontsize=18)
    plt.yticks(fontsize=18)
    ax.set_ylabel('Speedup', fontsize=18)
    ax.set_xlabel('')
    plt.savefig('./picture/figure_17.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/figure_17.pdf', bbox_inches='tight')
    plt.close()


if __name__ == "__main__":
    print(
        'Figure 4: Execution time for ResNet and VGG networks when batch size is 512. Networks with different structures fall on different lines.')
    print('Filename: figure_4')
    bs512resnetvgg()
    print(
        'Figure 5: The DNNs execution time is linearly correlated with the batch size. However, the slope differs from network to network.')
    print('Filename: figure_5')
    timevarybs()
    print(
        'Figure 6: GPUs can achieve a steady computing throughput when the batch size is larger than a certain value. When the batch size is small, the achieved FLOPS are lower.')
    print('Filename: figure_6')
    flopsvarybs()
    print('Figure 7: Different types of DNNs layers fall on different linear trend lines.')
    print('Filename: figure_7')
    scatteroflayer()
    print(
        'Figure 8: We classify kernels into three categories, including (top) input-driven kernels, (mid) operation-driven kernels, and (bottom) output-driven kernels. We show the linear relationship between different factors and execution time. We demonstrate that the classification can amplify the linear relationship.')
    print('Filename: figure_8')
    convpatternv3()

    print(
        'Figure 9: The efficiency of memory bandwidth and compute of ResNet-18 on different GPUs. The bandwidth efficiency is relatively stable across GPUs, but not the compute efficiency.')
    print('Filename: figure_9')
    bwflopsratio()

    print(
        'Figure 15: The predicted execution time of ResNet-50 on TITAN RTX with modified bandwidth. The red line represents the bandwidth of TITAN RTX, 672 GB/s.')
    print('Filename: figure_15')
    casestudy2_2()
    print(
        'Figure 16: The predicted execution time of DenseNet-169 on TITAN RTX with modified memory bandwidth. The red line represents the bandwidth of TITAN RTX, 672 GB/s.')
    print('Filename: figure_16')
    casestudy2()
    print('Figure 17: Speedup over 8 GB/s network for different networks running on memory-disaggregated GPU systems.')
    print('Filename: figure_17')
    casestudy3_2()

    print(
        'Figure 18: Actual execution time and predicted time for different networks on A40 and TITAN RTX. The yellow cross indicates the GPU with a shorter time which we should choose.')
    print('Filename: figure_18')
    casestudy2_8()
