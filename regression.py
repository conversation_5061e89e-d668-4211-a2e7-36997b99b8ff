"""
深度神经网络执行时间预测 - 主回归分析模块

该模块是整个项目的核心分析组件，用于：
1. 加载不同GPU的性能数据
2. 进行回归分析和模型验证
3. 计算预测误差和准确率
4. 生成性能分析图表

支持的GPU类型：
- NVIDIA A100
- NVIDIA A40
- NVIDIA TITAN RTX
- NVIDIA GTX 1080Ti

主要功能：
- 端到端(E2E)模型误差分析
- 逐层(LW)模型误差分析
- 内核级(KW)模型误差分析
- 改进内核级(IGKW)模型误差分析
- 生成论文中的所有性能分析图表

作者：DataDrivenDNNTimePrediction项目组
"""

import random
import numpy as np
import pandas
import matplotlib.pyplot as plt
import sklearn
import statsmodels.api as sm
from sklearn.model_selection import train_test_split
import seaborn as sns


class regressionlist:
    """
    回归模型信息存储类

    用于存储每个内核组的回归模型参数
    """
    def __init__(self):
        self.groupid = None      # 内核组ID
        self.const = None        # 常数项
        self.xvar = None         # 自变量系数
        self.xvarname = ''       # 自变量名称


def plotSfic(errorres, filename):
    """
    绘制预测误差分布图

    Args:
        errorres: 误差结果列表
        filename: 输出文件名

    生成散点图显示预测时间与实际时间的比值分布
    """
    fig, axes = plt.subplots(figsize=(5, 2.2))
    ax2 = axes
    xid = np.arange(1, len(errorres) + 1)

    # 创建散点图
    sns.scatterplot(x=xid,
                    y=errorres,
                    s=20,                    # 点大小
                    alpha=1,                 # 透明度
                    legend=None,
                    color=['#002c53'],       # 深蓝色
                    ec='#002c53',           # 边缘颜色
                    ax=ax2)

    # 设置坐标轴
    ax2.set_yscale('log')                   # Y轴使用对数刻度
    ax2.set_ylabel('Pred/Measured Time')    # Y轴标签：预测时间/实际时间
    ax2.set_xlabel('Percentage of Test Set') # X轴标签：测试集百分比
    ax2.grid(color='#DCDCDC')               # 添加网格

    # 保存图片（PNG和PDF格式）
    plt.savefig('./picture/' + filename + '.png', bbox_inches='tight', dpi=600)
    plt.savefig('./picture/' + filename + '.pdf', bbox_inches='tight')
    plt.close()


def regressionres(gpuname):
    """
    GPU性能回归分析主函数

    Args:
        gpuname: GPU名称（如'A100', 'TITAN', 'A40', '1080Ti'）

    功能：
    1. 加载指定GPU的HuggingFace和PyTorch模型性能数据
    2. 进行多次随机训练/测试集划分
    3. 建立回归模型并计算预测误差
    4. 输出平均误差率

    Returns:
        平均误差率列表
    """
    erroravg = []  # 存储每次实验的平均误差

    # 进行10次独立实验，减少随机性影响
    for i in range(10):
        # 加载HuggingFace模型数据
        df = pandas.read_csv(
            './data/' + gpuname + 'hugkernelregression.csv', low_memory=False)
        df['type'] = 'hug'  # 标记数据来源

        # 加载PyTorch模型数据
        dftorch = pandas.read_csv('./data/' + gpuname + 'torchkernelregression.csv', low_memory=False)
        dftorch['type'] = 'torch'  # 标记数据来源

        # 合并两个数据集
        df = pandas.concat([dftorch, df])
        dfm = df
        dfmodelid = df['modelid'].unique()

        testmodel = random.sample(list(dfmodelid), int(len(dfmodelid) * 0.15))
        i = 0
        for testmodelid in testmodel:
            df = df.drop(df[(df['modelid'] == testmodelid)].index)
            if i == 0:
                dftest = dfm[(dfm['modelid'] == testmodelid)]
            else:
                dftest = pandas.concat([dftest, dfm[(dfm['modelid'] == testmodelid)]], ignore_index=True)
            i += 1
        dfgroup = df.groupby(['type', "group"])
        regressionresults = []

        for name, group in dfgroup:
            dfitem = group
            if len(dfitem) == 0:
                break
            feature = dfitem.iloc[0, 28].replace(' ', '')
            if feature == 'no':
                # no feature
                const = dfitem.iloc[0, 30]
                xvar = 0
                xvarname = 'constant'
            elif feature == 'constant' or feature == 'contsant':
                # constant
                const = dfitem.iloc[0, 30]
                xvar = 0
                xvarname = 'constant'
            else:
                if feature == 'input':
                    # input
                    xvarname = 'inputproducts'
                elif feature == 'output':
                    # output
                    xvarname = 'outputproducts'
                else:  # ops
                    xvarname = 'ops'
                cols = [xvarname]
                x = dfitem[cols]
                y = dfitem['kernelduration']
                x = sm.add_constant(x)
                x = x.astype(float)
                model = sm.OLS(y, x)
                result = model.fit()
                const = 0
                coef_names = result.params.index.tolist()
                for cname in coef_names:
                    if cname == 'const':
                        const = result.params['const']
                    else:
                        xvar = result.params[xvarname]

            regres = regressionlist()
            regres.const = const
            regres.xvar = xvar
            regres.xvarname = xvarname
            regres.groupid = name
            regressionresults.append(regres)
        dftestgroup = dftest.groupby(['type', "group"])
        frames = []
        for name, group in dftestgroup:
            for item in regressionresults:
                if name == item.groupid:
                    group['y_pred'] = float(item.xvar) * group[item.xvarname].astype(float) + float(item.const)
                    frames.append(group)
        dffinal = pandas.concat(frames)
        error = []
        sum_train = []
        sum_test = []
        res = []
        dffinalgroup = dffinal.groupby("modelid")
        for name, group in dffinalgroup:
            sumtrain = group['kernelduration'].sum()
            sumtest = pandas.to_numeric(group['y_pred']).sum()
            error.append(abs(sumtest - sumtrain) / sumtrain)
            res.append(sumtest / sumtrain)
            sum_train.append(sumtrain)
            sum_test.append(sumtest)
        erroravg.append(sum(error) / len(error))
        res = sorted(res)
        if gpuname == 'A100':
            plotSfic(res, 'figure_13_A100')

    rounded_number = round(sum(erroravg) / len(erroravg), 4)
    print(gpuname + ' error rate')
    print(rounded_number)


def regressionresnlp():
    erroravg = []
    for i in range(10):

        df = pandas.read_csv(
            './data/A100nlpregression.csv',
        )
        df['type'] = 'nlp'
        dfm = df
        dfmodelid = df['modelid'].unique()

        testmodel = random.sample(list(dfmodelid), int(len(dfmodelid) * 0.15))
        i = 0
        for testmodelid in testmodel:
            df = df.drop(df[(df['modelid'] == testmodelid)].index)
            if i == 0:
                dftest = dfm[(dfm['modelid'] == testmodelid)]
            else:
                dftest = pandas.concat([dftest, dfm[(dfm['modelid'] == testmodelid)]], ignore_index=True)

            i += 1
        dfgroup = df.groupby(['type', "group"])
        regressionresults = []

        for name, group in dfgroup:
            dfitem = group
            if len(dfitem) == 0:
                break
            feature = dfitem.iloc[0, 21].replace(' ', '')
            if feature == 'no':
                # no feature
                const = dfitem.iloc[0, 23]
                xvar = 0
                xvarname = 'constant'
            elif feature == 'constant' or feature == 'contsant':
                # constant
                const = dfitem.iloc[0, 23]
                xvar = 0
                xvarname = 'constant'
            else:
                if feature == 'input':
                    # input
                    xvarname = 'inputproducts'
                elif feature == 'output':
                    # output
                    xvarname = 'outputproducts'
                else:  # ops
                    xvarname = 'ops'
                cols = [xvarname]
                x = dfitem[cols]
                y = dfitem['kernelduration']
                x = sm.add_constant(x)
                x = x.astype(float)
                model = sm.OLS(y, x)
                result = model.fit()
                const = 0
                coef_names = result.params.index.tolist()
                for cname in coef_names:
                    if cname == 'const':
                        const = result.params['const']
                    else:
                        xvar = result.params[xvarname]

            regres = regressionlist()
            regres.const = const
            regres.xvar = xvar
            regres.xvarname = xvarname
            regres.groupid = name
            regressionresults.append(regres)
        dftestgroup = dftest.groupby(['type', "group"])
        frames = []
        for name, group in dftestgroup:
            for item in regressionresults:
                if name == item.groupid:
                    group['y_pred'] = float(item.xvar) * group[item.xvarname].astype(float) + float(item.const)
                    frames.append(group)
        dffinal = pandas.concat(frames)
        error = []
        sum_train = []
        sum_test = []
        res = []
        dffinalgroup = dffinal.groupby("modelid")
        for name, group in dffinalgroup:
            sumtrain = group['kernelduration'].sum()
            sumtest = pandas.to_numeric(group['y_pred']).sum()
            error.append(abs(sumtest - sumtrain) / sumtrain)
            res.append(sumtest / sumtrain)
            sum_train.append(sumtrain)
            sum_test.append(sumtest)
        erroravg.append(sum(error) / len(error))
        sorted(res)
    rounded_number = round(sum(erroravg) / len(erroravg), 4)
    print(rounded_number)


def smols(x_train, x_test, y_train, y_test):
    x_train = sm.add_constant(x_train)
    model = sm.OLS(y_train, x_train)
    result = model.fit()
    y_predict = result.predict(sm.add_constant(x_test))
    res = y_predict / y_test
    return y_predict, res


def regressionresLayerandModel(typename):
    df = pandas.read_csv("./data/bstimehug.csv")
    df_zscore = df.copy()
    cols = ['convlayertotal', 'normalization', 'avgpool',
            'fcclayer', 'memory access', 'ops', 'bs=512']
    df = df.drop(df[(df['type'] == 'outliers')].index)
    for col in cols:
        df_col = df[col]
        z_score = (df_col - df_col.mean()) / df_col.std()
        df_zscore[col] = z_score.abs() > 1

    df_drop_outlier = df[df_zscore['ops'].reindex(df.index) == False]
    df_drop_outlier = df[df_zscore['bs=512'].reindex(df.index) == False]

    df = df_drop_outlier
    df = sklearn.utils.shuffle(df)
    if typename == 'layer':
        cols = ['convlayertotal', 'normalization', 'avgpool',
                'fcclayer']
        filename = 'figure_12_A100'
    elif typename == 'model':
        cols = ['ops']
        filename = 'figure_11_A100'

    x = df[cols]
    y = df['bs=512']

    erroravg = []
    for i in range(10):
        x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.15)
        y_pred, res = smols(x_train, x_test, y_train, y_test)
        res = sorted(res)
        plotSfic(res, filename)
        test = np.sum(np.absolute(y_pred - y_test) / y_test) / len(y_test)
        erroravg.append(test)
    rounded_number = round(sum(erroravg) / len(erroravg), 4)
    print(rounded_number)


def regressionfuncdiffgpu():
    # Load the datasets
    dfbg1 = pandas.read_csv('./data/1080Tihugkernelregression.csv', low_memory=False)
    dfbg6 = pandas.read_csv('./data/TITANhugkernelregression.csv', low_memory=False)
    dfbg13 = pandas.read_csv('./data/A100hugkernelregression.csv', low_memory=False)
    dfbg14 = pandas.read_csv('./data/A40hugkernelregression.csv', low_memory=False)
    dfbg1torch = pandas.read_csv(
        './data/1080Titorchkernelregression.csv', low_memory=False)
    dfbg6torch = pandas.read_csv(
        './data/TITANtorchkernelregression.csv', low_memory=False)
    dfbg13torch = pandas.read_csv(
        './data/A100torchkernelregression.csv', low_memory=False)
    dfbg14torch = pandas.read_csv(
        './data/A40torchkernelregression.csv', low_memory=False)
    dfbg1['type'] = 'hug'
    dfbg1torch['type'] = 'torch'
    dfbg6['type'] = 'hug'
    dfbg6torch['type'] = 'torch'
    dfbg13['type'] = 'hug'
    dfbg13torch['type'] = 'torch'
    dfbg14['type'] = 'hug'
    dfbg14torch['type'] = 'torch'
    dfbg1 = pandas.concat([dfbg1, dfbg1torch], ignore_index=True)
    dfbg6 = pandas.concat([dfbg6, dfbg6torch], ignore_index=True)
    dfbg13 = pandas.concat([dfbg13, dfbg13torch], ignore_index=True)
    dfbg14 = pandas.concat([dfbg14, dfbg14torch], ignore_index=True)
    dfbg1['gpu'] = 'bg1'
    dfbg6['gpu'] = 'bg6'
    dfbg13['gpu'] = 'bg13'
    dfbg14['gpu'] = 'bg14'

    dfbg1['memory'] = 11
    dfbg6['memory'] = 24
    dfbg13['memory'] = 40
    dfbg14['memory'] = 48

    dfbg1['tc'] = 0
    dfbg6['tc'] = 576
    dfbg13['tc'] = 432
    dfbg14['tc'] = 336

    dfbg1['tflops'] = 11.34
    dfbg6['tflops'] = 16.31
    dfbg13['tflops'] = 19.5
    dfbg14['tflops'] = 37.42

    dfbg1['sm'] = 28
    dfbg6['sm'] = 72
    dfbg13['sm'] = 108
    dfbg14['sm'] = 84

    dfbg1['bw'] = 484
    dfbg6['bw'] = 672
    dfbg13['bw'] = 1555
    dfbg14['bw'] = 696

    dfbg1['tcsm'] = 0
    dfbg6['tcsm'] = 8
    dfbg13['tcsm'] = 4
    dfbg14['tcsm'] = 4

    df = pandas.concat([dfbg1, dfbg13, dfbg14], ignore_index=True)
    dftest = dfbg6

    dfgroup = df.groupby(['type', "kernelname"])
    regressionresults = []

    for name, group in dfgroup:
        dfitem = group
        dfgpugroup = dfitem.groupby(['gpu'])
        igpu = 0
        for gpuname, gpugroup in dfgpugroup:
            igpu += 1
        if igpu <= 2:
            continue
        if len(dfitem) == 0:
            break
        xvar = []
        xvarname = []
        featureops = False
        featureinput = False
        featureoutput = False
        for ii in range(len(dfitem)):
            if dfitem.iloc[0, 28].replace(' ', '') == 'input':
                featureinput = True
            elif dfitem.iloc[0, 28].replace(' ', '') == 'output':
                featureoutput = True
            elif dfitem.iloc[0, 28].replace(' ', '') == 'ops':
                featureops = True
        feature = 'ops'
        if featureops == True:
            feature = 'ops'
        elif featureinput == True:
            feature = 'input'
        elif featureoutput == True:
            feature = 'output'
        cpuevent = dfitem.iloc[0, 2]
        if feature == 'no':
            # no feature
            const = dfitem.iloc[0, 30]
            xvar.append(0)
            xvarname.append('constant')
        elif feature == 'constant' or feature == 'contsant':
            # constant
            const = dfitem.iloc[0, 30]
            xvar.append(0)
            xvarname.append('constant')
        else:
            if feature == 'input':
                # input
                yvar = dfitem['inputproducts']
            elif feature == 'output':
                # output
                yvar = dfitem['outputproducts']
            else:  # ops
                yvar = dfitem['ops']

            if cpuevent.strip() != 'aten::conv2d':
                cols = ['bw']  # 'sm','tc'
                dfitem['flops'] = yvar / dfitem['kernelduration']
                x = dfitem[cols]
                y = dfitem['flops']
                x = sm.add_constant(x)
                x = x.astype(float)
                model = sm.OLS(y, x)
                result = model.fit()
                const = 0
                coef_names = result.params.index.tolist()
                for cname in coef_names:
                    if cname == 'const':
                        const = result.params['const']
                    elif cname == 'memory':
                        xvar.append(result.params['memory'])
                        xvarname.append('memory')
                    elif cname == 'tflops':
                        xvar.append(result.params['tflops'])
                        xvarname.append('tflops')
                    else:
                        xvar.append(result.params['bw'])
                        xvarname.append('bw')
            else:
                cols = ['bw']
                dfitem['flops'] = yvar / dfitem['kernelduration']
                x = dfitem[cols]
                y = dfitem['flops']
                x = sm.add_constant(x)
                x = x.astype(float)
                model = sm.OLS(y, x)
                result = model.fit()
                const = 0
                coef_names = result.params.index.tolist()
                for cname in coef_names:
                    if cname == 'const':
                        const = result.params['const']
                    elif cname == 'memory':
                        xvar.append(result.params['memory'])
                        xvarname.append('memory')
                    elif cname == 'tflops':
                        xvar.append(result.params['tflops'])
                        xvarname.append('tflops')
                    elif cname == 'tc':
                        xvar.append(result.params['tc'])
                        xvarname.append('tc')
                    elif cname == 'tcsm':
                        xvar.append(result.params['tcsm'])
                        xvarname.append('tcsm')
                    elif cname == 'sm':
                        xvar.append(result.params['sm'])
                        xvarname.append('sm')
                    else:
                        xvar.append(result.params['bw'])
                        xvarname.append('bw')

            regres = regressionlist()
            regres.const = const
            regres.xvar = xvar
            regres.xvarname = xvarname
            regres.groupid = name
            regres.feature = feature
            regressionresults.append(regres)
    dftestgroup = dftest.groupby(['type', "kernelname"])
    frames = []
    for name, group in dftestgroup:
        if len(group) == 0:
            break
        feature = group.iloc[0, 28].replace(' ', '')
        if feature == 'no':
            # no feature
            yvarops = 0
        elif feature == 'constant' or feature == 'contsant':
            # constant
            yvarops = 0
        else:
            if feature == 'input':
                # input
                yvarops = group['inputproducts']
            elif feature == 'output':
                # output
                yvarops = group['outputproducts']
            else:  # ops
                yvarops = group['ops']
            for item in regressionresults:
                if name == item.groupid:
                    group['flops_pred'] = 0
                    for i in range(len(item.xvar)):
                        group['flops_pred'] += float(item.xvar[i]) * group[item.xvarname[i]].astype(float)
                    group['flops_pred'] += float(item.const)
                    if item.feature == 'input':
                        # input
                        yvarops = group['inputproducts']
                    elif item.feature == 'output':
                        # output
                        yvarops = group['outputproducts']
                    else:  # ops
                        yvarops = group['ops']
                    group['y_pred'] = yvarops / group['flops_pred']
                    frames.append(group)

    dffinal = pandas.concat(frames)
    error = []
    res = []
    dffinalgroup = dffinal.groupby("modelid")
    for name, group in dffinalgroup:
        sumtrain = group['kernelduration'].sum()
        sumtest = pandas.to_numeric(group['y_pred']).sum()
        err = abs(sumtest - sumtrain) / sumtrain
        if err <= 10:
            error.append(err)
        res.append(sumtest / sumtrain)
    rounded_number = round(sum(error) / len(error), 4)
    print(rounded_number)
    res = sorted(res)
    plotSfic(res, 'figure_14_TITAN')


def regressionResnetVaryBS(bs):
    erroravg = []
    for i in range(1):

        df = pandas.read_csv(
            './data/bs' + bs + 'dataprocesswithops-regression.csv', low_memory=False)
        df['type'] = 'hug'
        dftorch = pandas.read_csv(
            './data/bs' + bs + 'dataprocesswithops-regression-torch.csv', low_memory=False)
        dftorch['type'] = 'torch'
        df = pandas.concat([dftorch, df])
        dfm = df
        dfmodelid = df['modelid'].unique()
        testmodel = list(['resnet50.json   '])
        i = 0
        for testmodelid in testmodel:
            df = df.drop(df[(df['modelid'] == testmodelid)].index)
            if i == 0:
                dftest = dfm[(dfm['modelid'] == testmodelid)]
            else:
                dftemp = dfm[(dfm['modelid'] == testmodelid)]
                dftest = pandas.concat([dftest, dftemp])
            i += 1
        dfgroup = df.groupby(['type', "group"])
        regressionresults = []

        for name, group in dfgroup:
            dfitem = group
            if len(dfitem) == 0:
                break
            cols = ['ops']
            feature = dfitem.iloc[0, 21].replace(' ', '')
            if feature == 'no':
                # no feature
                const = dfitem.iloc[0, 23]
                xvar = 0
                xvarname = 'constant'
            elif feature == 'constant' or feature == 'contsant':
                # constant
                const = dfitem.iloc[0, 23]
                xvar = 0
                xvarname = 'constant'
            else:
                if feature == 'input':
                    # input
                    xvarname = 'inputproducts'
                elif feature == 'output':
                    # output
                    xvarname = 'outputproducts'
                else:  # ops
                    xvarname = 'ops'
                cols = [xvarname]
                x = dfitem[cols]
                y = dfitem['kernelduration']
                x = sm.add_constant(x)
                x = x.astype(float)
                model = sm.OLS(y, x)
                result = model.fit()
                const = 0
                coef_names = result.params.index.tolist()
                for cname in coef_names:
                    if cname == 'const':
                        const = result.params['const']
                    else:
                        xvar = result.params[xvarname]

            regres = regressionlist()
            regres.const = const
            regres.xvar = xvar
            regres.xvarname = xvarname
            regres.groupid = name
            regressionresults.append(regres)
        dftestgroup = dftest.groupby(['type', "group"])
        frames = []
        for name, group in dftestgroup:
            for item in regressionresults:
                if name == item.groupid:
                    group['y_pred'] = float(item.xvar) * group[item.xvarname].astype(float) + float(item.const)
                    frames.append(group)
        dffinal = pandas.concat(frames)
        error = []
        sum_train = []
        sum_test = []
        res = []
        dffinalgroup = dffinal.groupby("modelid")
        for name, group in dffinalgroup:
            sumtrain = group['kernelduration'].sum()
            sumtest = pandas.to_numeric(group['y_pred']).sum()
            error.append(abs(sumtest - sumtrain) / sumtrain)
            res.append(sumtest / sumtrain)
            sum_train.append(sumtrain)
            sum_test.append(sumtest)
        rounded_number = round(sum(error) / len(error), 4)
        erroravg.append(rounded_number)
        res = sorted(res)
    print(sum(erroravg) / len(erroravg))


if __name__ == "__main__":
    print('Error rate are the average when run 10 times')
    print('For image classification DNNs :')
    print('E2E model error rate on A100 (Figure 11):')
    regressionresLayerandModel('model')
    print('LW model error rate on A100 (Figure 12):')
    regressionresLayerandModel('layer')
    print('KW model (Figure 13):')
    regressionres('1080Ti')
    regressionres('TITAN')
    regressionres('A40')
    regressionres('A100')
    print('KW model extension for Transformers, error rate on A100:')
    regressionresnlp()
    print('IGKW model, error rate on TITAN RTX (Figure 14):')
    regressionfuncdiffgpu()
    print('Table 2, Modeling ResNet-50 performance by KW model on V100')
    print("BS = 64, error rate:")
    regressionResnetVaryBS('64')
    print("BS = 128, error rate:")
    regressionResnetVaryBS('128')
    print("BS = 256, error rate:")
    regressionResnetVaryBS('256')
