# 项目代码结构说明

本文档详细说明了DataDrivenDNNTimePrediction项目的代码结构和各模块功能。

## 项目目录结构

```
DataDrivenDNNTimePrediction/
├── Readme.md                          # 项目说明文档
├── CODE_STRUCTURE.md                  # 代码结构说明(本文件)
├── install.sh                         # Case 1依赖安装脚本
├── installNewGPU.sh                   # Case 2依赖安装脚本
├── run.sh                             # Case 1主运行脚本
├── runNewGPU.sh                       # Case 2主运行脚本
├── regression.py                      # 主回归分析模块
├── plotscattervarybsmodels.py         # 基础性能图表生成
├── picturepart.py                     # 详细分析图表生成
├── data/                              # 性能数据目录
│   ├── A100hugkernelregression.csv    # A100 HuggingFace模型数据
│   ├── A100torchkernelregression.csv  # A100 PyTorch模型数据
│   ├── TITANhugkernelregression.csv   # TITAN RTX HuggingFace数据
│   ├── TITANtorchkernelregression.csv # TITAN RTX PyTorch数据
│   ├── A40hugkernelregression.csv     # A40 HuggingFace数据
│   ├── A40torchkernelregression.csv   # A40 PyTorch数据
│   ├── 1080Tihugkernelregression.csv  # GTX 1080Ti HuggingFace数据
│   ├── 1080Titorchkernelregression.csv# GTX 1080Ti PyTorch数据
│   ├── bstimehug.csv                  # 批大小时间数据
│   └── paper/                         # 论文相关数据
├── picture/                           # 生成图表保存目录
├── code/                              # 核心代码目录
│   ├── huggingface/                   # HuggingFace模型处理
│   │   ├── dataprocess.py             # Chrome trace数据处理
│   │   ├── flopsmodels.py             # FLOPs计算
│   │   ├── runmodels.py               # 性能测试和profiling
│   │   ├── mapkernelops.py            # 内核-操作映射
│   │   ├── regressionfunction.py     # 回归建模
│   │   ├── regressionresult.py       # 回归结果分析
│   │   ├── modelinfo.txt             # 模型列表
│   │   ├── networklist.txt           # 网络列表
│   │   ├── runflops.sh               # FLOPs计算脚本
│   │   ├── runmodel.sh               # 模型运行脚本
│   │   └── traceprofiler/            # Chrome trace文件存储
│   ├── torch/                        # PyTorch模型处理
│   │   ├── dataprocess.py            # 数据处理(与HF版本类似)
│   │   ├── flopsmodels.py            # FLOPs计算
│   │   ├── runmodels.py              # 性能测试
│   │   ├── mapkernelops.py           # 内核映射
│   │   ├── traceprofiler/            # trace文件存储
│   │   └── vgg16-397923af.pth        # 预训练权重
│   ├── thop/                         # 自定义FLOPs计算库
│   │   ├── profile.py                # 主profiling接口
│   │   ├── basic_hooks.py            # 基础操作hooks
│   │   └── value.py                  # 数值处理
│   └── ILSVRC2012_img_val/           # ImageNet验证集
└── logs/                             # 日志文件(可选)
```

## 核心模块详解

### 1. 数据收集模块

#### HuggingFace流水线 (`code/huggingface/`)
- **flopsmodels.py**: 计算652个HuggingFace模型的理论FLOPs
- **runmodels.py**: 在GPU上运行模型，收集执行时间和Chrome trace
- **dataprocess.py**: 解析trace文件，提取CPU操作和CUDA内核信息
- **mapkernelops.py**: 将内核数据与FLOPs数据关联

#### PyTorch流水线 (`code/torch/`)
- 功能与HuggingFace流水线类似，但针对传统CNN模型优化
- 支持ResNet、VGG、DenseNet等经典架构

### 2. 建模分析模块

#### 回归建模 (`regressionfunction.py`)
- 按CUDA内核名称分组
- 自动选择最佳预测特征(输入/输出/操作驱动)
- 建立线性回归模型

#### 性能分析 (`regression.py`)
- 加载多GPU性能数据
- 进行交叉验证
- 计算预测误差率
- 支持E2E、LW、KW、IGKW等多种模型

### 3. 可视化模块

#### 基础图表 (`plotscattervarybsmodels.py`)
- FLOPs vs 执行时间散点图
- 批大小影响分析

#### 详细分析 (`picturepart.py`)
- 生成论文中的18个分析图表
- GPU效率对比
- 网络架构性能特征
- 带宽敏感性分析

## 数据流程

### Case 1: 基于已有数据分析
```
data/*.csv → regression.py → picture/*.png/pdf
     ↓
plotscattervarybsmodels.py → 基础图表
     ↓
picturepart.py → 详细图表
```

### Case 2: 新GPU数据收集
```
模型列表 → FLOPs计算 → 性能测试 → 数据处理 → 内核映射 → 回归建模 → 误差验证
   ↓           ↓          ↓         ↓        ↓        ↓        ↓
modelinfo.txt → flops.csv → trace/ → process.csv → withops.csv → regression.csv → 误差率
```

## 关键算法

### 1. 内核分类算法
```python
# 三种驱动模式的自动选择
score_input = regressor_input.score(X_input, Y)    # 输入驱动
score_output = regressor_output.score(X_output, Y)  # 输出驱动  
score_ops = regressor_ops.score(X_ops, Y)          # 操作驱动

best_feature = max(score_input, score_output, score_ops)
```

### 2. 时间重叠处理
```python
# 计算CUDA内核的重叠和非重叠时间
cudatime = maxcuda - mincuda              # 考虑重叠的总时间
cudatimenooverlap = sum(kernel_durations) # 不考虑重叠的累加时间
```

### 3. 特征工程
- **输入特征**: 输入张量元素总数
- **输出特征**: 输出张量元素总数  
- **操作特征**: FLOPs数量
- **架构特征**: 网络类型、层类型等

## 扩展指南

### 添加新GPU支持
1. 在新GPU上运行`runNewGPU.sh`
2. 收集性能数据
3. 将数据文件添加到`data/`目录
4. 更新`regression.py`中的GPU列表

### 添加新模型
1. 将模型ID添加到`modelinfo.txt`
2. 确保模型可通过HuggingFace或PyTorch加载
3. 重新运行数据收集流程

### 自定义分析
1. 修改`picturepart.py`添加新图表
2. 在`regression.py`中添加新的误差分析方法
3. 扩展`regressionfunction.py`支持新的建模方法

## 性能优化建议

### 数据收集优化
- 使用SSD存储trace文件
- 增加GPU内存以支持更大批大小
- 使用多进程并行处理模型

### 建模优化
- 考虑非线性回归模型
- 添加更多特征工程
- 使用集成学习方法

### 可视化优化
- 使用交互式图表库(如Plotly)
- 添加实时性能监控
- 支持自定义图表配置

---

本文档随项目更新而更新，最后修改时间：2024年
