#!/usr/bin/env bash
sudo apt install python3-pip
pip install virtualenv
virtualenv venvproject --python=python3.8
#we use python3.8.10
cd venvproject/
source ./bin/activate

pip install torch==1.13.1+cu116 torchvision==0.14.1+cu116 torchaudio==0.13.1 --extra-index-url https://download.pytorch.org/whl/cu116

pip install thop
pip show thop
#usually, it will be under ./venv/lib/python3.8/site-packages
#replace the file based on the file directory
cd
cd DNNTimePrediction
cp code/thop/profile.py ../venvproject/lib/python3.8/site-packages/thop/profile.py
cp code/thop/basic_hooks.py  ../venvproject/lib/python3.8/site-packages/thop/vision/basic_hooks.py
cp code/thop/value.py  ../venvproject/lib/python3.8/site-packages/thop/vision/

pip install pandas
pip install 'transformers[torch]'
pip install -U scikit-learn
pip install matplotlib
pip install statsmodels
pip install -U seaborn

