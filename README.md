
# 基于数据驱动的深度神经网络执行时间预测

本项目提供了一个完整的深度学习模型性能预测系统，能够准确预测不同GPU上各种深度神经网络的执行时间。

## 项目简介

随着深度学习模型的快速发展和GPU硬件的不断更新，准确预测模型在不同GPU上的执行时间变得越来越重要。本项目通过大规模数据收集、内核级性能分析和智能回归建模，实现了高精度的DNN执行时间预测。

**核心贡献:**
- 提出了内核级性能预测方法，将预测精度提升至85%以上
- 建立了包含652个模型、4种GPU的大规模性能数据集
- 实现了自动化的数据收集和建模流程
- 提供了完整的性能分析和可视化工具

## 项目特点

- **大规模模型支持**: 支持652个HuggingFace预训练模型和多种PyTorch经典模型
- **多GPU兼容**: 支持A100、A40、TITAN RTX、GTX 1080Ti等主流GPU
- **内核级分析**: 提供CUDA内核级别的详细性能分析
- **智能建模**: 自动选择最佳预测特征（输入驱动/输出驱动/操作驱动）
- **高精度预测**: 平均预测误差低于15%

## 使用场景

### Case 1: 基于已有GPU数据集进行预测分析

适用于研究人员和工程师分析现有GPU的性能特征，生成性能分析报告。

#### 安装依赖
```bash
chmod +x install.sh
./install.sh
```

#### 运行分析
```bash
chmod +x run.sh
./run.sh
```

#### 输出结果
程序将输出以下内容并生成18个性能分析图表：

**所有图表保存在: ./picture/ 目录**

**图表3 (figure_3)**: FLOPs与执行时间关系分析
- 展示批大小≥4时，所有网络的执行时间与FLOPs的关系
- 验证了DNN执行时间与FLOPs的线性相关性
- 揭示了小操作数量时的例外情况

**图表4 (figure_4)**: ResNet和VGG网络性能对比
- 批大小为512时ResNet和VGG网络的执行时间
- 显示不同网络结构具有不同的性能特征线

**图表5 (figure_5)**: 批大小对执行时间的影响
- 证明DNN执行时间与批大小呈线性相关
- 展示不同网络具有不同的斜率特征

**图表6 (figure_6)**: GPU计算吞吐量分析
- 显示GPU在批大小超过某个阈值时能达到稳定的计算吞吐量
- 揭示小批大小时FLOPS利用率较低的现象

**图表7 (figure_7)**: 不同DNN层类型的性能特征
- 展示不同类型的DNN层具有不同的线性趋势线
- 为层级性能建模提供理论基础

**图表8 (figure_8)**: 内核分类与线性关系
- 将内核分为三类：输入驱动、操作驱动、输出驱动
- 展示不同因素与执行时间的线性关系
- 证明分类能够增强线性关系的准确性

**图表9 (figure_9)**: 不同GPU的带宽和计算效率
- 分析ResNet-18在不同GPU上的内存带宽和计算效率
- 显示带宽效率在GPU间相对稳定，但计算效率差异较大

**图表15 (figure_15)**: ResNet-50带宽敏感性分析
- 预测ResNet-50在TITAN RTX上随带宽变化的执行时间
- 红线表示TITAN RTX的实际带宽(672 GB/s)

**图表16 (figure_16)**: DenseNet-169带宽敏感性分析
- 预测DenseNet-169在TITAN RTX上随内存带宽变化的执行时间
- 红线表示TITAN RTX的实际带宽(672 GB/s)

**图表17 (figure_17)**: 内存分离GPU系统加速比
- 显示不同网络在内存分离GPU系统上相对于8 GB/s网络的加速比
- 为分布式GPU系统设计提供参考

**图表18 (figure_18)**: 实际vs预测执行时间对比
- 对比A40和TITAN RTX上不同网络的实际执行时间和预测时间
- 黄色十字标记表示应选择的更快GPU

#### 模型预测准确率统计

**图像分类DNN模型误差率分析:**

- **E2E模型 (端到端)** 在A100上的误差率 (图表11)
- **LW模型 (逐层)** 在A100上的误差率 (图表12)
- **KW模型 (内核级)** 误差率分析 (图表13)
  - 支持GPU: 1080Ti, TITAN, A40, A100
- **KW模型扩展** 用于Transformer架构，A100上的误差率
- **IGKW模型 (改进内核级)** 在TITAN RTX上的误差率 (图表14)

**ResNet-50性能建模 (KW模型在V100上)**
- 批大小 = 64: 误差率统计
- 批大小 = 128: 误差率统计
- 批大小 = 256: 误差率统计

---

### Case 2: 新GPU数据收集与性能预测

适用于在新GPU上收集性能数据并建立预测模型的场景。

#### 环境配置要求
请确保系统环境配置如下：
1. **操作系统**: Ubuntu 20.04.5 LTS
2. **CUDA版本**: CUDA 11.6
3. **cuDNN版本**: cuDNN 8.3.2

> **重要**: 请严格按照上述环境配置，确保数据收集的准确性

#### 安装依赖
```bash
chmod +x installNewGPU.sh
./installNewGPU.sh
```

#### 数据收集
运行完整的数据收集流程：
```bash
chmod +x runNewGPU.sh
./runNewGPU.sh
```

该脚本将自动完成：
1. PyTorch模型FLOPs计算和性能测试
2. HuggingFace模型FLOPs计算和性能测试
3. Chrome trace数据处理和内核映射
4. 回归模型建立和参数优化

#### 性能预测

**计算所有网络的平均误差率:**
```bash
python regressionresult.py ./bs128dataprocesswithops_regression_hug.csv ./bs128dataprocesswithops_regression_torch.csv
```

**计算特定网络(如ResNet-50)的误差率:**
```bash
python regressionresult.py ./bs128dataprocesswithops_regression_hug.csv ./bs128dataprocesswithops_regression_torch.csv resnet50
```

> **说明**: 所有支持的网络模型列表请参考 `networklist.txt` 文件

---

## 技术架构

### 数据收集流程
1. **FLOPs计算**: 使用自定义thop库计算模型理论计算量
2. **性能测试**: 在真实GPU上运行模型，收集执行时间
3. **内核分析**: 使用PyTorch Profiler收集CUDA内核详细信息
4. **数据映射**: 将内核执行数据与FLOPs数据关联

### 建模方法
- **内核分组**: 按CUDA内核名称分组建立独立模型
- **特征选择**: 自动选择最佳预测特征(输入/输出/操作驱动)
- **线性回归**: 为每个内核组建立线性预测模型
- **模型验证**: 通过交叉验证评估预测准确性

### 支持的模型架构
- **传统CNN**: ResNet, VGG, DenseNet, MobileNet等
- **现代架构**: Vision Transformer, ConvNeXt, Swin Transformer
- **专用模型**: 目标检测、语义分割等任务的专用模型

## 使用注意事项

### 硬件要求
- **GPU**: 支持CUDA的NVIDIA GPU (推荐RTX系列或数据中心GPU)
- **内存**: 至少16GB系统内存，8GB GPU内存
- **存储**: 至少50GB可用空间用于存储数据和模型

### 软件依赖
- **Python**: 3.7-3.9 (推荐3.8)
- **PyTorch**: 1.10+ (需要CUDA支持)
- **Transformers**: 4.15+ (用于HuggingFace模型)
- **其他**: 详见install.sh和installNewGPU.sh

### 常见问题

**Q: 为什么需要ImageNet验证集?**
A: 使用真实图像数据能更准确地反映模型的实际性能，避免随机数据的偏差。

**Q: 可以在其他GPU上运行吗?**
A: 可以，但需要重新收集该GPU的性能数据(运行Case 2流程)。

**Q: 预测精度如何?**
A: 内核级模型(KW)平均误差率约为10-15%，端到端模型(E2E)约为20-25%。

**Q: 支持自定义模型吗?**
A: 支持，只需将模型添加到相应的模型列表文件中即可。

## 引用

如果本项目对您的研究有帮助，请引用我们的论文：

```bibtex
@article{dnn_time_prediction,
  title={Data-Driven Deep Neural Network Execution Time Prediction},
  author={DataDrivenDNNTimePrediction Team},
  journal={Conference/Journal Name},
  year={2024}
}
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**最后更新**: 2024年

