#！/bin/bash
cd
echo ${PWD}
export root_path=${PWD}
cd ~/xiaokai/DataDrivenDNN/code/huggingface
# rm -r ${root_path}/.cache/huggingface/hub/*
python runmodels.py 0 128 > bstime.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python runmodels.py 100 128 >> bstime.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python runmodels.py 200 128 >> bstime.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python runmodels.py 300 128 >> bstime.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python runmodels.py 400 128 >> bstime.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python runmodels.py 500 128 >> bstime.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
