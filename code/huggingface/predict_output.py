"""
模型执行时间对比分析模块

该模块用于比较模型的实际运行时间与预测执行时间，
生成详细的对比报告和可视化结果。

主要功能：
1. 加载回归模型和测试数据
2. 计算预测执行时间
3. 对比实际时间与预测时间
4. 生成详细的对比报告
5. 输出误差统计信息

作者：DataDrivenDNNTimePrediction项目组
"""

import pandas as pd
import statsmodels.api as sm
import sys
import random
import numpy as np
from datetime import datetime


class RegressionModel:
    """回归模型类，存储回归系数和特征信息"""
    def __init__(self):
        self.groupid = None
        self.const = None
        self.xvar = None
        self.xvarname = ''


def load_and_prepare_data(filenamehug, filenametorch):
    """加载并准备数据"""
    # 加载HuggingFace数据
    df_hug = pd.read_csv(filenamehug)
    df_hug['type'] = 'hug'
    
    # 加载Torch数据
    df_torch = pd.read_csv(filenametorch)
    df_torch['type'] = 'torch'
    
    # 合并数据
    df = pd.concat([df_torch, df_hug], ignore_index=True)
    return df


def train_regression_models(df_train):
    """训练回归模型"""
    dfgroup = df_train.groupby(['type', "group"])
    regression_models = []
    
    for name, group in dfgroup:
        if len(group) == 0:
            continue
            
        # 获取特征类型
        feature = group.iloc[0, 21].replace(' ', '')
        
        model = RegressionModel()
        model.groupid = name
        
        if feature == 'no' or feature == 'constant' or feature == 'contsant':
            # 常数模型
            model.const = group.iloc[0, 23]
            model.xvar = 0
            model.xvarname = 'constant'
        else:
            # 线性回归模型
            if feature == 'input':
                model.xvarname = 'inputproducts'
            elif feature == 'output':
                model.xvarname = 'outputproducts'
            else:  # ops
                model.xvarname = 'ops'
                
            # 准备特征和目标变量
            x = group[[model.xvarname]]
            y = group['kernelduration']
            x = sm.add_constant(x)
            x = x.astype(float)
            
            # 训练模型
            ols_model = sm.OLS(y, x)
            result = ols_model.fit()
            
            # 提取系数
            model.const = result.params['const']
            model.xvar = result.params[model.xvarname]
            
        regression_models.append(model)
    
    return regression_models


def predict_execution_time(df_test, regression_models):
    """预测执行时间"""
    dftestgroup = df_test.groupby(['type', "group"])
    frames = []
    
    for name, group in dftestgroup:
        # 找到对应的回归模型
        model = None
        for reg_model in regression_models:
            if name == reg_model.groupid:
                model = reg_model
                break
        
        if model is None:
            continue
            
        # 计算预测时间
        if model.xvarname == 'constant':
            group['y_pred'] = float(model.const)
        else:
            group['y_pred'] = (float(model.xvar) * group[model.xvarname].astype(float) + 
                              float(model.const))
        
        frames.append(group)
    
    if frames:
        return pd.concat(frames, ignore_index=True)
    else:
        return pd.DataFrame()


def calculate_model_level_metrics(df_results):
    """计算模型级别的指标"""
    dffinalgroup = df_results.groupby("modelid")
    model_metrics = []
    
    for model_id, group in dffinalgroup:
        # 实际总时间
        actual_time = group['kernelduration'].sum()
        
        # 预测总时间
        predicted_time = group['y_pred'].sum()
        
        # 计算误差
        absolute_error = abs(predicted_time - actual_time)
        relative_error = absolute_error / actual_time if actual_time > 0 else 0
        ratio = predicted_time / actual_time if actual_time > 0 else 0
        
        model_metrics.append({
            'modelid': model_id,
            'actual_time': actual_time,
            'predicted_time': predicted_time,
            'absolute_error': absolute_error,
            'relative_error': relative_error,
            'ratio': ratio,
            'layer_count': len(group)
        })
    
    return pd.DataFrame(model_metrics)


def generate_comparison_report(model_metrics, output_file=None):
    """生成对比报告"""
    print("=" * 80)
    print("模型执行时间对比分析报告")
    print("=" * 80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"分析模型数量: {len(model_metrics)}")
    print()
    
    # 基本统计
    print("基本统计信息:")
    print(f"  平均相对误差: {model_metrics['relative_error'].mean():.4f}")
    print(f"  相对误差标准差: {model_metrics['relative_error'].std():.4f}")
    print(f"  中位数相对误差: {model_metrics['relative_error'].median():.4f}")
    print(f"  最大相对误差: {model_metrics['relative_error'].max():.4f}")
    print(f"  最小相对误差: {model_metrics['relative_error'].min():.4f}")
    print()
    
    # 预测精度分布
    print("预测精度分布:")
    error_ranges = [
        (0, 0.1, "误差 < 10%"),
        (0.1, 0.2, "10% ≤ 误差 < 20%"),
        (0.2, 0.5, "20% ≤ 误差 < 50%"),
        (0.5, 1.0, "50% ≤ 误差 < 100%"),
        (1.0, float('inf'), "误差 ≥ 100%")
    ]
    
    for min_err, max_err, label in error_ranges:
        count = len(model_metrics[(model_metrics['relative_error'] >= min_err) & 
                                 (model_metrics['relative_error'] < max_err)])
        percentage = count / len(model_metrics) * 100
        print(f"  {label}: {count} 个模型 ({percentage:.1f}%)")
    print()
    
    # 最佳和最差预测
    print("预测表现:")
    best_models = model_metrics.nsmallest(5, 'relative_error')
    worst_models = model_metrics.nlargest(5, 'relative_error')
    
    print("  最佳预测 (相对误差最小):")
    for _, row in best_models.iterrows():
        print(f"    {row['modelid']}: 实际={row['actual_time']:.2f}μs, "
              f"预测={row['predicted_time']:.2f}μs, 误差={row['relative_error']:.4f}")
    
    print("\n  最差预测 (相对误差最大):")
    for _, row in worst_models.iterrows():
        print(f"    {row['modelid']}: 实际={row['actual_time']:.2f}μs, "
              f"预测={row['predicted_time']:.2f}μs, 误差={row['relative_error']:.4f}")
    
    # 保存详细结果
    if output_file:
        model_metrics.to_csv(output_file, index=False)
        print(f"\n详细结果已保存到: {output_file}")
    
    return model_metrics


def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("使用方法: python model_time_comparison.py <huggingface_csv> <torch_csv> [test_model_name] [output_file]")
        sys.exit(1)
    
    filenamehug = sys.argv[1]
    filenametorch = sys.argv[2]
    testmodelname = sys.argv[3] if len(sys.argv) > 3 else ''
    output_file = sys.argv[4] if len(sys.argv) > 4 else 'model_time_comparison.csv'
    
    print("正在加载数据...")
    df = load_and_prepare_data(filenamehug, filenametorch)
    
    # 准备训练和测试数据
    if testmodelname == '':
        # 随机选择15%的模型作为测试集
        dfmodelid = df['modelid'].unique()
        test_models = random.sample(list(dfmodelid), int(len(dfmodelid) * 0.15))
    else:
        test_models = [testmodelname + '.json   ']
    
    # 分离训练和测试数据
    df_test = df[df['modelid'].isin(test_models)]
    df_train = df[~df['modelid'].isin(test_models)]
    
    print(f"训练集模型数量: {df_train['modelid'].nunique()}")
    print(f"测试集模型数量: {df_test['modelid'].nunique()}")
    
    print("正在训练回归模型...")
    regression_models = train_regression_models(df_train)
    print(f"训练了 {len(regression_models)} 个回归模型")
    
    print("正在预测执行时间...")
    df_results = predict_execution_time(df_test, regression_models)
    
    if df_results.empty:
        print("错误: 无法生成预测结果")
        return
    
    print("正在计算模型级别指标...")
    model_metrics = calculate_model_level_metrics(df_results)
    
    print("正在生成对比报告...")
    generate_comparison_report(model_metrics, output_file)


if __name__ == "__main__":
    main()