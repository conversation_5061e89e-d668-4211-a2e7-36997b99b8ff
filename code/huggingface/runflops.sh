#！/bin/bash
cd
echo ${PWD}
export root_path=${PWD}
cd ~/xiaokai/DataDrivenDNN/code/huggingface
# rm -r ${root_path}/.cache/huggingface/hub/*
python flopsmodels.py 0 128 > bsflops.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python flopsmodels.py 100 128 >> bsflops.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python flopsmodels.py 200 128 >> bsflops.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python flopsmodels.py 300 128 >> bsflops.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python flopsmodels.py 400 128 >> bsflops.csv
# rm -r ${root_path}/.cache/huggingface/hub/*
python flopsmodels.py 500 128 >> bsflops.csv
# rm -r ${root_path}/.cache/huggingface/hub/*