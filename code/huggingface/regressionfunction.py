"""
回归建模模块

该模块用于为每种CUDA内核类型建立线性回归预测模型，
是整个性能预测系统的核心建模组件。

主要功能：
1. 读取完整的性能分析数据（包含内核信息和FLOPs信息）
2. 按内核名称分组，为每种内核建立独立的预测模型
3. 自动选择最佳预测特征（输入驱动/输出驱动/操作驱动）
4. 生成线性回归模型参数
5. 输出带有预测模型信息的完整数据集

核心思想：
- 不同类型的CUDA内核有不同的性能特征
- 有些内核的执行时间主要取决于输入数据量（输入驱动）
- 有些内核主要取决于输出数据量（输出驱动）
- 有些内核主要取决于计算量（操作驱动）
- 通过比较R²分数自动选择最佳特征

使用方法：
python regressionfunction.py <input_file> > <output_file>

作者：DataDrivenDNNTimePrediction项目组
"""

import pandas as pd
from sklearn.linear_model import LinearRegression
import sys

def regressionfunc(filename):
    """
    主回归建模函数

    Args:
        filename: 输入数据文件路径（来自mapkernelops.py的输出）
    """
    # 加载数据集
    df = pd.read_csv(filename)
    df.replace(' None ', 0, inplace=True)  # 将空值替换为0

    # 处理aten::cat操作的特殊情况
    # cat操作通常没有独立的输入维度信息，需要从下一行借用
    indices = df[df['cpueventname'] == '  aten::cat  '].index

    for index in indices:
        if index + 1 < len(df):
            df.at[index, ' inputdims'] = df.at[index + 1, ' inputdims']
            df.at[index, 'inputproducts'] = df.at[index + 1, 'inputproducts']
            df.at[index, 'inputsize'] = df.at[index + 1, 'inputsize']

    # 按内核名称分组，为每种内核建立独立的回归模型
    data = df.groupby("kernelname")

    # 为数据框添加回归模型相关的列
    df['feature'] = None      # 最佳特征类型（input/output/ops/constant）
    df['group'] = None        # 内核组ID
    df['constant'] = None     # 常数项（用于常数模型）
    df['slope'] = None        # 斜率（线性模型的系数）
    df['intercepts'] = None   # 截距（线性模型的截距）

    # 初始化变量
    regressors = []  # 回归器列表（保留用于扩展）
    groupid = 0      # 组ID计数器

    # 输出CSV文件头
    print('modelid,layerid,cpueventname,cudatime,cudatimenooverlap,',
          'inputdims,inputproducts,inputsize,kernelduration,blocksperSM,',
          'warpsperSM,stream,grid,block,kernelname,outputdims,outputproducts,outputsize,ops,infeatures,outfeatures,feature,group,constant,slope,intercepts')
    # 遍历每个内核组，建立回归模型
    for name, group in data:
        groupid += 1

        # 如果该内核组只有一个样本，使用常数模型
        if len(group) == 1:
            feature = 'constant'
            constant = group['kernelduration'].mean()  # 使用平均值作为常数
            slope = 0
            intercepts = 0
        else:
            # 有多个样本，尝试三种不同的线性回归模型

            # 1. 输入驱动模型：执行时间 = f(输入数据量)
            X = group['inputproducts'].values.reshape(-1, 1)  # 输入张量元素总数
            Y = group['kernelduration'].values.reshape(-1, 1)  # 内核执行时间
            regressor_input = LinearRegression()
            regressor_input.fit(X, Y)
            score_input = regressor_input.score(X, Y)  # R²分数，衡量拟合质量

            # 2. 输出驱动模型：执行时间 = f(输出数据量)
            X = group['outputproducts'].values.reshape(-1, 1)  # 输出张量元素总数
            regressor_output = LinearRegression()
            regressor_output.fit(X, Y)
            score_output = regressor_output.score(X, Y)

            # 3. 操作驱动模型：执行时间 = f(计算量)
            X = group['ops'].values.reshape(-1, 1)  # FLOPs数量
            regressor_ops = LinearRegression()
            regressor_ops.fit(X, Y)
            score_ops = regressor_ops.score(X, Y)

            # 选择R²分数最高的模型作为最佳模型
            max_value = max(score_input, score_output, score_ops)

            if max_value == 0:
                # 如果所有模型的R²都为0，说明线性关系很弱，使用常数模型
                feature = 'constant'
                constant = group['kernelduration'].mean()
                slope = 0
                intercepts = 0
            elif max_value == score_ops:
                # 操作驱动模型最佳：执行时间主要取决于计算量
                feature = 'ops'
                constant = 0
                slope = regressor_ops.coef_[0][0]        # 线性系数
                intercepts = regressor_ops.intercept_[0]  # 截距
            elif max_value == score_output:
                # 输出驱动模型最佳：执行时间主要取决于输出数据量
                feature = 'output'
                constant = 0
                slope = regressor_output.coef_[0][0]
                intercepts = regressor_output.intercept_[0]
            elif max_value == score_input:
                # 输入驱动模型最佳：执行时间主要取决于输入数据量
                feature = 'input'
                constant = 0
                slope = regressor_input.coef_[0][0]
                intercepts = regressor_input.intercept_[0]

        # 将回归模型参数填入该组的所有样本
        for index in range(len(group)):
            group.iloc[index, 21] = feature      # 最佳特征类型
            group.iloc[index, 22] = groupid      # 内核组ID
            group.iloc[index, 23] = constant     # 常数项
            group.iloc[index, 24] = slope        # 斜率
            group.iloc[index, 25] = intercepts   # 截距

            # 输出完整的数据行（原始数据 + 回归模型参数）
            print(group.iloc[index, 0], ',', group.iloc[index, 1], ',', group.iloc[index, 2], ',', group.iloc[index, 3], ',',
                  group.iloc[index, 4], ',', group.iloc[index, 5], ',', group.iloc[index, 6], ',', group.iloc[index, 7], ',',
                  group.iloc[index, 8], ',', group.iloc[index, 9], ',', group.iloc[index, 10], ',', group.iloc[index, 11], ',',
                  group.iloc[index, 12], ',', group.iloc[index, 13], ',', group.iloc[index, 14], ',', group.iloc[index, 15], ',',
                  group.iloc[index, 16], ',', group.iloc[index, 17], ',', group.iloc[index, 18], ',', group.iloc[index, 19], ',',
                  group.iloc[index, 20], ',', group.iloc[index, 21], ',', group.iloc[index, 22], ',', group.iloc[index, 23], ',',
                  group.iloc[index, 24], ',', group.iloc[index, 25])


if __name__ == "__main__":
    """
    主程序入口

    使用方法：
    python regressionfunction.py <input_file> > <output_file>

    示例：
    python regressionfunction.py bs128dataprocesswithops.csv > bs128dataprocesswithops_regression.csv

    输出文件将包含原始数据以及每个内核的回归模型参数，
    这些参数可用于预测新模型的执行时间。
    """
    filename = sys.argv[1]
    regressionfunc(filename)