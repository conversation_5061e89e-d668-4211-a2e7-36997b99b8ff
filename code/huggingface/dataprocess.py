"""
HuggingFace模型性能数据处理模块

该模块用于解析PyTorch Profiler生成的Chrome trace文件，
提取CPU操作和CUDA内核的详细性能信息，用于深度学习模型执行时间预测。

主要功能：
1. 解析Chrome trace JSON文件
2. 提取CPU操作信息（如aten::conv2d, aten::linear等）
3. 提取CUDA内核执行信息
4. 建立CPU操作与CUDA内核的关联关系
5. 输出结构化的性能数据用于后续分析

作者：DataDrivenDNNTimePrediction项目组
"""

import json
import os


class Kernellist:
    """
    CUDA内核信息存储类

    用于存储单个CUDA内核的详细执行信息，包括：
    - 基本信息：名称、执行时间、时间戳
    - GPU资源使用：寄存器、共享内存、warp分配
    - 执行配置：grid和block维度、stream信息
    """
    def __init__(self):
        self.name = ''                    # 内核名称（如ampere_sgemm_128x128_nn）
        self.id = None                    # 内核ID
        self.duration = None              # 内核执行时间（微秒）
        self.starttime = None             # 开始时间戳
        self.endtime = None               # 结束时间戳
        self.correlationid = None         # 关联ID，用于与CPU操作关联
        self.registersperthread = None    # 每线程寄存器数量
        self.sharedmemory = None          # 共享内存使用量（字节）
        self.blocksperSM = None           # 每个流式多处理器的block数量
        self.warpsperSM = None            # 每个流式多处理器的warp数量
        self.grid = ''                    # Grid维度配置（如[2, 2, 1536]）
        self.block = ''                   # Block维度配置（如[256, 1, 1]）
        self.stream = None                # CUDA流ID


class Operatorlist:
    """
    CPU操作信息存储类

    用于存储PyTorch CPU操作的信息，包括：
    - 操作基本信息：名称、执行时间
    - 输入数据信息：维度、大小
    - 关联的CUDA事件列表
    """
    def __init__(self):
        self.name = ''                    # 操作名称（如aten::conv2d, aten::linear）
        self.id = None                    # 操作ID
        self.duration = None              # CPU操作执行时间（微秒）
        self.starttime = None             # 开始时间戳
        self.endtime = None               # 结束时间戳
        self.correlationid = []           # 关联的CUDA内核ID列表
        self.cudaevents = []              # 关联的CUDA事件列表
        self.inputdims = ''               # 输入张量维度信息


def l_prod(in_obj):
    """
    计算（可能嵌套的）列表/元组/可转整数字符串的乘积

    Args:
        in_obj: 数值列表、嵌套列表、元组或可转整数的字符串

    Returns:
        int: 所有数值元素的乘积

    用途：计算张量的总元素数量（如[128, 3, 224, 224] -> 19267584）
    """
    res = 1
    def _acc(x):
        nonlocal res
        if isinstance(x, (list, tuple)):
            for v in x:
                _acc(v)
        else:
            try:
                res *= int(x)
            except Exception:
                pass
    _acc(in_obj)
    return res


def dataprocess():
    """
    主数据处理函数

    解析traceprofiler目录下的所有Chrome trace JSON文件，
    提取CPU操作和CUDA内核信息，并建立它们之间的关联关系。

    处理流程：
    1. 遍历所有trace文件
    2. 解析CPU操作事件（aten::*操作）
    3. 解析CUDA运行时事件（cudaLaunchKernel）
    4. 解析CUDA内核事件
    5. 建立CPU操作与CUDA内核的关联
    6. 输出结构化数据

    输出格式：CSV格式，包含模型ID、层ID、操作名称、执行时间等信息
    """
    path = './traceprofiler/'  # Chrome trace文件存储路径
    files = os.listdir(path)

    # 输出CSV文件头
    print(
        'modelid,layerid,cpueventname,cudatime,cudatimenooverlap,inputdims,'
        'inputproducts,inputsize,kernelduration,blocksperSM,'
        'warpsperSM,stream,grid,block,kernelname')

    fileid = 0
    for file in files:
        fileid += 1
        if fileid >= 100:  # 限制处理文件数量，避免内存溢出
            break

        # 读取并解析JSON trace文件
        with open(path + file, "r") as f:
            json_trace = json.load(f)

        cpuevents = []          # 存储CPU操作事件列表
        profilerstarttime = 0   # Profiler开始时间戳
        # 第一遍遍历：提取CPU操作事件
        for event in json_trace['traceEvents']:
            # 识别CPU操作事件（PyTorch操作，如aten::conv2d, aten::linear等）
            if (event.get('cat', '').lower() == 'cpu_op') or (event.get('cat', '').lower() == 'operator') and event.get(
                    'ph', '').lower() == 'x':

                # 提取事件基本信息
                dur = event['dur']    # 持续时间（微秒）
                ts = event['ts']      # 开始时间戳
                te = ts + dur         # 结束时间戳

                # 创建操作对象并填充信息
                popitem = []
                aoperator = Operatorlist()
                aoperator.name = event['name']                    # 操作名称
                aoperator.duration = dur                          # 执行时间
                aoperator.starttime = ts                          # 开始时间
                aoperator.endtime = te                            # 结束时间
                aoperator.inputdims = event['args'].get('Input Dims', []) # 输入维度信息

                cpuevents.append(aoperator)

                # 处理嵌套操作：移除被包含的操作，保留最外层操作
                # 这是为了避免重复计算嵌套调用的执行时间
                for cpueventsitem in cpuevents:
                    # 检查时间重叠情况
                    if (te <= cpueventsitem.endtime and ts > cpueventsitem.starttime) or (
                            te < cpueventsitem.endtime and ts >= cpueventsitem.starttime) \
                            or (
                            te == cpueventsitem.endtime and ts == cpueventsitem.starttime and aoperator.name != cpueventsitem.name):
                        popitem.append(aoperator)  # 当前操作被包含，标记删除
                    elif te >= cpueventsitem.endtime and ts < cpueventsitem.starttime:
                        popitem.append(cpueventsitem)  # 已有操作被包含，标记删除

                # 移除被标记的重复操作
                for item in popitem:
                    if item in cpuevents:
                        cpuevents.remove(item)


            # 识别CUDA运行时事件（cudaLaunchKernel调用）
            elif (event.get('cat', '').lower() == 'cuda_runtime') or (
                    event.get('cat', '').lower() == 'runtime') and event.get('ph', '').lower() == 'x' and event.get(
                'name', '').lower() == 'cudalaunchkernel':

                # 提取CUDA启动事件信息
                dur = event['dur']
                ts = event['ts']
                te = ts + dur
                correlationid = event['args']["correlation"]  # 关联ID，用于连接CPU操作和GPU内核

                # 将CUDA启动事件与对应的CPU操作关联
                for cpueventsitem in cpuevents:
                    # 如果CPU操作包含了这个CUDA启动事件，则建立关联
                    if cpueventsitem.endtime > te and cpueventsitem.starttime < ts:
                        cpueventsitem.correlationid.append(correlationid)

            # 记录Profiler开始时间，用于后续时间戳校准
            elif event.get('name', '') == 'Iteration Start: PyTorch Profiler':
                profilerstarttime = event.get('ts')

        # 第二遍遍历：提取CUDA内核事件并与CPU操作关联
        for event in json_trace['traceEvents']:
            # 识别CUDA内核执行事件
            if event.get('cat', '').lower() == 'kernel' and event.get('ph', '').lower() == 'x':
                correlationid = event['args']["correlation"]  # 关联ID
                dur = event['dur']    # 内核执行时间
                ts = event['ts']      # 开始时间戳
                te = ts + dur         # 结束时间戳

                cudaevents = []
                # 查找与此内核关联的CPU操作
                for cpueventsitem in cpuevents:
                    if correlationid in cpueventsitem.correlationid:
                        # 创建内核对象并填充详细信息
                        akernel = Kernellist()
                        akernel.name = event['name']                                    # 内核名称
                        akernel.duration = dur                                          # 执行时间
                        akernel.starttime = ts                                          # 开始时间
                        akernel.endtime = te                                            # 结束时间
                        akernel.correlationid = correlationid                          # 关联ID
                        akernel.registersperthread = event['args']['registers per thread']  # 每线程寄存器数
                        akernel.sharedmemory = event['args']['shared memory']          # 共享内存使用量
                        akernel.blocksperSM = ''  # 注释掉：event['args']['blocks per SM']  # 每SM的block数
                        akernel.warpsperSM = event['args']['warps per SM']             # 每SM的warp数
                        akernel.grid = event['args']['grid']                           # Grid配置
                        akernel.block = event['args']['block']                         # Block配置
                        akernel.stream = event['args']['stream']                       # CUDA流ID

                        cudaevents.append(akernel)
                        cpueventsitem.cudaevents.append(cudaevents)  # 将内核事件添加到对应的CPU操作
        # 处理每个CPU操作，计算相关统计信息并输出
        layerid = 0
        for cpueventsitem in cpuevents:
            layerid += 1

            # 计算输入张量信息
            # 归一化输入形状，防止 [[N,C,H,W]] 这类多重嵌套导致乘积失败
            inputshape = []
            dims = cpueventsitem.inputdims
            if isinstance(dims, (list, tuple)) and len(dims) > 0:
                inputshape = dims[0]
                while isinstance(inputshape, (list, tuple)) and len(inputshape) == 1 and isinstance(inputshape[0], (list, tuple)):
                    inputshape = inputshape[0]

            inputproducts = l_prod(inputshape)  # 输入张量总元素数
            inputsize = inputshape              # 输入张量维度

            # 针对卷积操作提取额外信息
            if cpueventsitem.name == 'aten::conv2d':
                bias = cpueventsitem.inputdims[2]               # 偏置信息
                kernelsize = cpueventsitem.inputdims[1][2:]     # 卷积核大小 (Kw x Kh)
            else:
                bias = 0
                kernelsize = 0

            # 计算CUDA执行时间统计
            cudatime = 0          # 总CUDA时间（考虑重叠）
            mincuda = 0           # 最早开始时间
            maxcuda = 0           # 最晚结束时间
            cudatimenooverlap = 0 # 总CUDA时间（不考虑重叠）

            # 遍历所有关联的CUDA事件，计算时间统计
            for cudaeventsitem in cpueventsitem.cudaevents:
                for item in cudaeventsitem:
                    # 计算相对于profiler开始时间的时间戳
                    if mincuda == 0:
                        mincuda = item.starttime - profilerstarttime
                    elif mincuda > item.starttime - profilerstarttime:
                        mincuda = item.starttime - profilerstarttime
                    if maxcuda < item.endtime - profilerstarttime:
                        maxcuda = item.endtime - profilerstarttime
                    cudatimenooverlap += item.endtime - item.starttime  # 累加所有内核时间

            cudatime = maxcuda - mincuda  # 考虑重叠的总时间

            # 输出每个内核的详细信息
            for cudaeventsitem in cpueventsitem.cudaevents:
                for item in cudaeventsitem:
                    print(file, ',', layerid, ',', cpueventsitem.name, ',', cudatime, ',', cudatimenooverlap, ',',
                          str(cpueventsitem.inputdims).replace(' ', '').replace(',', ';')  # 输入维度
                          , ',', inputproducts, ',', str(inputsize).replace(',', ';'), ',',  # 输入信息
                          item.duration, ',', item.blocksperSM, ',',                        # 内核执行时间和SM配置
                          item.warpsperSM, ',', item.stream, ',', str(item.grid).replace(',', ';'), ',',  # GPU资源配置
                          str(item.block).replace(',', ';'), ',',                           # Block配置
                          item.name.replace(',', ';'))                                     # 内核名称


if __name__ == "__main__":
    """
    主程序入口

    直接运行此脚本时，会处理traceprofiler目录下的所有Chrome trace文件，
    并将结果输出到标准输出（通常重定向到CSV文件）。

    使用方法：
    python dataprocess.py > bs128dataprocess.csv
    """
    dataprocess()
