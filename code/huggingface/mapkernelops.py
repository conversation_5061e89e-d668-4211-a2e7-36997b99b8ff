"""
内核-操作映射模块

该模块用于将CUDA内核执行数据与FLOPs计算数据进行关联，
建立完整的性能分析数据集。

主要功能：
1. 读取dataprocess.py生成的内核执行数据
2. 读取flopsmodels.py生成的FLOPs数据
3. 根据操作类型进行精确匹配
4. 为每个内核添加对应的理论计算量信息
5. 输出完整的性能分析数据集

使用方法：
python mapkernelops.py <datafile> <flopsfile>
- datafile: 内核执行数据文件（来自dataprocess.py）
- flopsfile: FLOPs数据文件（来自flopsmodels.py）

作者：DataDrivenDNNTimePrediction项目组
"""

import sys
import pandas as pd


def l_prod(in_list):
    """
    计算列表中所有元素的乘积

    Args:
        in_list: 数值列表

    Returns:
        int: 所有元素的乘积

    用途：计算张量的总元素数量
    """
    res = 1
    for _ in in_list:
        res *= _
    return res


def mapkernelops(datafile, flopsfile):
    """
    主映射函数：将内核数据与FLOPs数据关联

    Args:
        datafile: 内核执行数据文件路径
        flopsfile: FLOPs数据文件路径
    """
    # 输出CSV文件头
    print('modelid,layerid,cpueventname,cudatime,cudatimenooverlap,',
          'inputdims,inputproducts,inputsize,kernelduration,blocksperSM,',
          'warpsperSM,stream,grid,block,kernelname,outputdims,outputproducts,outputsize,ops,infeatures,outfeatures')

    # 读取内核执行数据
    df = pd.read_csv(datafile,
                     usecols=['modelid', 'layerid', 'cpueventname', 'cudatime', 'cudatimenooverlap',
                              'inputdims', 'inputproducts', 'inputsize', 'kernelduration',
                              'blocksperSM', 'warpsperSM', 'stream', 'grid', 'block', 'kernelname'],
                     dtype={'modelid': str, 'kernelname': str}
                     )

    dfmodelid = df['modelid'].unique()  # 获取所有模型ID

    # 为数据框添加新列，用于存储FLOPs相关信息
    df['outputdims'] = None      # 输出维度
    df['outputproducts'] = None  # 输出元素总数
    df['outputsize'] = None      # 输出大小
    df['ops'] = None             # 操作的FLOPs数量
    df['infeatures'] = None      # 输入特征数（用于线性层）
    df['outfeatures'] = None     # 输出特征数（用于线性层）

    dfmodel = pd.DataFrame()     # 当前处理的模型数据
    tracename = ''               # 当前处理的模型名称
    # 读取FLOPs文件并进行映射
    with open(flopsfile) as f:
        for line in f.readlines():
            flopsinfo = line.strip()
            flopsinfo = flopsinfo.split(',')
            a = flopsinfo  # FLOPs信息数组

            # 检查是否切换到新模型
            if tracename != flopsinfo[0]:
                # 输出上一个模型的处理结果
                if (flopsinfo[0].replace('/', '&').replace(' ', '') + '.json ' in dfmodelid):
                    for index1, row1 in dfmodel.iterrows():
                        print(row1['modelid'], ',', row1['layerid'], ',', row1['cpueventname'], ',', row1['cudatime'],
                              ',', row1['cudatimenooverlap'], ',',
                              row1['inputdims'], ',', row1['inputproducts'], ',', row1['inputsize'], ',',
                              row1['kernelduration'], ',',
                              row1['blocksperSM'], ',', row1['warpsperSM'], ',', row1['stream'], ',', row1['grid'], ',',
                              row1['block'], ',', row1['kernelname'], ',',
                              row1['outputdims'], ',', row1['outputproducts'], ',', row1['outputsize'], ',',
                              row1['ops'], ',', row1['infeatures'], ',', row1['outfeatures'])

                    # 切换到新模型的数据
                    dfmodel = df[(df['modelid'] == flopsinfo[0].replace('/', '&').replace(' ', '') + '.json ')]

            tracename = flopsinfo[0]  # 更新当前模型名称
            # 遍历当前模型的所有内核数据，进行操作类型匹配
            for index2, row2 in dfmodel.iterrows():
                if (dfmodel.loc[index2, 'ops'] == None):  # 如果还未分配FLOPs信息

                    # 匹配卷积操作
                    if a[1].strip() == 'count_convNd' and row2['cpueventname'].strip() == 'aten::conv2d':
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]  # 获取同一层的所有内核
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'outputdims'] = a[4]                    # 输出维度
                            outputsize = a[4].replace('[', '').replace(']', '').split(';')
                            outputproduct = l_prod(outputsize)                          # 计算输出元素总数
                            dfmodel.loc[index3, 'outputproducts'] = outputproduct
                            dfmodel.loc[index3, 'outputsize'] = str(outputsize).replace(',', ';')
                            dfmodel.loc[index3, 'ops'] = a[2]                          # FLOPs数量
                        break
                    # 匹配归一化操作
                    elif a[1].strip() == 'count_normalization' and (
                            row2['cpueventname'].strip() == 'aten::batch_norm' or row2[
                        'cpueventname'].strip() == 'aten::layer_norm'):
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'outputdims'] = a[4]
                            outputsize = a[4].replace('[', '').replace(']', '').split(';')
                            outputproduct = l_prod(outputsize)
                            dfmodel.loc[index3, 'outputproducts'] = outputproduct
                            dfmodel.loc[index3, 'outputsize'] = str(outputsize).replace(',', ';')
                            dfmodel.loc[index3, 'ops'] = a[2]
                        break

                    # 匹配激活函数操作
                    elif (a[1].strip() == 'count_prelu' or a[1].strip() == 'count_relu') and (
                            row2['cpueventname'].strip() == 'aten::relu' or row2[
                        'cpueventname'].strip() == 'aten::relu_'):
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'outputdims'] = a[4]
                            outputsize = a[4].replace('[', '').replace(']', '').split(';')
                            outputproduct = l_prod(outputsize)
                            dfmodel.loc[index3, 'outputproducts'] = outputproduct
                            dfmodel.loc[index3, 'outputsize'] = str(outputsize).replace(',', ';')
                            dfmodel.loc[index3, 'ops'] = a[2]
                        break
                    # 匹配池化操作
                    elif (a[1].strip() == 'count_avgpool' and row2['cpueventname'].strip() == 'aten::avg_pool2d') \
                            or (a[1].strip() == 'count_adap_avgpool' and (
                            row2['cpueventname'].strip() == 'aten::adaptive_avg_pool2d' or row2[
                        'cpueventname'].strip() == 'aten::adaptive_avg_pool1d')) \
                            or (a[1].strip() == 'count_maxpool' and row2['cpueventname'].strip() == 'aten::max_pool2d ') \
                            or (a[1].strip() == 'count_adap_maxpool' and row2[
                        'cpueventname'].strip() == 'aten::adaptive_max_pool2d'):
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'outputdims'] = a[4]
                            outputsize = a[4].replace('[', '').replace(']', '').split(';')
                            outputproduct = l_prod(outputsize)
                            dfmodel.loc[index3, 'outputproducts'] = outputproduct
                            dfmodel.loc[index3, 'outputsize'] = str(outputsize).replace(',', ';')
                            dfmodel.loc[index3, 'ops'] = a[2]
                        break

                    # 匹配线性层操作
                    elif a[1].strip() == 'count_linear' and row2['cpueventname'].strip() == 'aten::linear':
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'infeatures'] = a[4]   # 输入特征数
                            dfmodel.loc[index3, 'outfeatures'] = a[5]  # 输出特征数
                            dfmodel.loc[index3, 'ops'] = a[2]          # FLOPs数量
                        break

                    else:
                        # 未匹配的操作类型，跳过
                        pass


if __name__ == "__main__":
    """
    主程序入口

    使用方法：
    python mapkernelops.py <datafile> <flopsfile>

    示例：
    python mapkernelops.py bs128dataprocess.csv bs128flops.csv > bs128dataprocesswithops.csv
    """
    datafile = sys.argv[1]   # 内核执行数据文件
    flopsfile = sys.argv[2]  # FLOPs数据文件
    mapkernelops(datafile, flopsfile)
