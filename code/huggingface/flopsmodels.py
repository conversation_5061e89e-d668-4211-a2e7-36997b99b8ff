"""
HuggingFace模型FLOPs计算模块

该模块用于计算HuggingFace预训练模型的理论计算量（FLOPs），
为后续的性能预测提供基础数据。

主要功能：
1. 加载HuggingFace预训练模型
2. 使用自定义的thop库计算详细的FLOPs信息
3. 分类统计不同类型操作的计算量
4. 输出结构化的FLOPs数据

使用方法：
python flopsmodels.py <skip_rows> <batch_size>
- skip_rows: 跳过的模型数量（用于分批处理）
- batch_size: 批处理大小

输出格式：
modelid, total_flops, params, mac_count, conv_ops, fc_ops, pool_ops, norm_ops, other_ops

作者：DataDrivenDNNTimePrediction项目组
"""

from transformers import AutoFeatureExtractor, AutoModelForImageClassification
import torch
from torchvision import transforms
import sys
import torchvision
from thop import profile  # 自定义的FLOPs计算库

# ImageNet标准数据预处理（虽然这里只用于模型加载，不用于实际推理）
data_transforms = {
    'predict': transforms.Compose([
        transforms.Resize(256),                                                    # 调整图像大小
        transforms.CenterCrop(224),                                               # 中心裁剪
        transforms.ToTensor(),                                                    # 转换为张量
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),  # 标准化
    ])
}

# 设备配置
device = torch.device("cuda")
starter = torch.cuda.Event(enable_timing=True)
ender = torch.cuda.Event(enable_timing=True)
torch.cuda.Event(enable_timing=True)

# 命令行参数解析
skiprows = int(sys.argv[1])  # 跳过的模型数量
bsinput = int(sys.argv[2])   # 批处理大小
# 读取模型列表并计算FLOPs
with open('./modelinfo.txt') as f:
    # 跳过指定数量的模型（用于分批处理）
    while skiprows != 0:
        f.readline()
        skiprows -= 1

    k = 0
    for line in f.readlines():
        k = k + 1
        if k > 100:  # 每批最多处理100个模型
            break
        try:
            modelid = line.strip()  # 获取模型ID
            # print(modelid)

            # 加载HuggingFace预训练模型
            # feature_extractor = AutoFeatureExtractor.from_pretrained(modelid)
            model = AutoModelForImageClassification.from_pretrained(modelid, use_safetensors=True)

            # 创建随机输入张量用于FLOPs计算
            # 形状：[batch_size, channels, height, width] = [bsinput, 3, 224, 224]
            input = torch.randn(bsinput, 3, 224, 224)

            # 使用自定义thop库计算详细的FLOPs信息
            flops, params, maccountly, convops, fccops, poolops, normops, otherops = profile(
                model, modelid, inputs=(input,)
            )

            # 输出详细的FLOPs统计信息
            # flops: 总FLOPs数量
            # params: 模型参数数量
            # maccountly: MAC (Multiply-Accumulate) 操作数量
            # convops: 卷积操作FLOPs
            # fccops: 全连接操作FLOPs
            # poolops: 池化操作FLOPs
            # normops: 归一化操作FLOPs
            # otherops: 其他操作FLOPs
            print(modelid, ",", flops, ",", params, ",", maccountly, ",",
                  convops, ",", fccops, ",", poolops, ",", normops, ",", otherops)

        except Exception as e:
            # 忽略加载失败的模型，继续处理下一个
            # print(e)
            pass
        

    
