import pandas
import statsmodels.api as sm
import sys
import random


class regressionlist:
    def __init__(self):
        self.groupid = None
        self.const = None
        self.xvar = None
        self.xvarname = ''


def regressionres(filenamehug, filenametorch, testmodelname):
    erroravg = []
    loopnum = 10
    if testmodelname != '':
        loopnum = 1
    for i in range(loopnum):

        df = pandas.read_csv(filenamehug)  # dtype={'ops': 'float64' }
        df['type'] = 'hug'

        dftorch = pandas.read_csv(filenametorch)
        dftorch['type'] = 'torch'
        df = pandas.concat([dftorch, df])

        testmodel = []
        dfm = df
        dfmodelid = df['modelid'].unique()
        if testmodelname == '':
            testmodel = random.sample(list(dfmodelid), int(len(dfmodelid) * 0.15))
        else:
            testmodel = list([testmodelname + '.json   '])
        i = 0
        for testmodelid in testmodel:
            df = df.drop(df[(df['modelid'] == testmodelid)].index)
            if i == 0:
                dftest = dfm[(dfm['modelid'] == testmodelid)]
            else:
                dftemp = dfm[(dfm['modelid'] == testmodelid)]
                dftest = pandas.concat([dftest, dftemp])  # , ignore_index=True)
            i += 1
        dfgroup = df.groupby(['type', "group"])
        regressionresults = []

        if len(dftest) == 0:
            print('no test network, check the name')
            return
        for name, group in dfgroup:
            dfitem = group
            if len(dfitem) == 0:
                break
            cols = ['ops']
            feature = dfitem.iloc[0, 21].replace(' ', '')
            if feature == 'no':
                # no feature
                const = dfitem.iloc[0, 23]
                xvar = 0
                xvarname = 'constant'
            elif feature == 'constant' or feature == 'contsant':
                # constant
                const = dfitem.iloc[0, 23]
                xvar = 0
                xvarname = 'constant'
            else:
                if feature == 'input':
                    # input
                    xvarname = 'inputproducts'
                elif feature == 'output':
                    # output
                    xvarname = 'outputproducts'
                else:  # ops
                    xvarname = 'ops'
                cols = [xvarname]
                x = dfitem[cols]
                y = dfitem['kernelduration']
                x = sm.add_constant(x)
                x = x.astype(float)
                model = sm.OLS(y, x)
                result = model.fit()
                const = 0
                coef_names = result.params.index.tolist()
                for cname in coef_names:
                    if cname == 'const':
                        const = result.params['const']
                    else:
                        xvar = result.params[xvarname]

            regres = regressionlist()
            regres.const = const
            regres.xvar = xvar
            regres.xvarname = xvarname
            regres.groupid = name
            regressionresults.append(regres)
        dftestgroup = dftest.groupby(['type', "group"])
        frames = []
        for name, group in dftestgroup:
            for item in regressionresults:
                if name == item.groupid:
                    group['y_pred'] = float(item.xvar) * group[item.xvarname].astype(float) + float(item.const)
                    frames.append(group)
        dffinal = pandas.concat(frames)
        error = []
        sum_train = []
        sum_test = []
        res = []
        predict_results = []
        dffinalgroup = dffinal.groupby("modelid")
        for name, group in dffinalgroup:
            sumtrain = group['kernelduration'].sum()
            sumtest = pandas.to_numeric(group['y_pred']).sum()
            error.append(abs(sumtest - sumtrain) / sumtrain)
            res.append(sumtest / sumtrain)
            sum_train.append(sumtrain)
            sum_test.append(sumtest)
            predict_results.append([name, sumtrain, sumtest, error])
        erroravg.append(sum(error) / len(error))
        res = sorted(res)
    print(sum(erroravg) / len(erroravg))
    # 转成 DataFrame 后保存
    results_df = pandas.DataFrame(predict_results, columns=["modelid", "actual_time", "predicted_time", "relative_error"])
    results_df.to_csv("prediction_results.csv", index=False)

if __name__ == "__main__":
    filenamehug = sys.argv[1]
    filenametorch = sys.argv[2]
    testmodelname = ''
    if len(sys.argv) >= 4:
        testmodelname = sys.argv[3]
    regressionres(filenamehug, filenametorch, testmodelname)
