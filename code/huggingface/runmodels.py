"""
HuggingFace模型性能测试和Profiling模块

该模块用于测试HuggingFace预训练模型在GPU上的执行性能，
包括执行时间测量和详细的CUDA内核profiling。

主要功能：
1. 加载HuggingFace预训练模型
2. 使用ImageNet验证集进行推理测试
3. 测量准确的GPU执行时间
4. 生成详细的Chrome trace文件用于性能分析

使用方法：
python runmodels.py <skip_rows> <batch_size>
- skip_rows: 跳过的模型数量（用于分批处理）
- batch_size: 批处理大小

作者：DataDrivenDNNTimePrediction项目组
"""

from transformers import AutoFeatureExtractor, AutoModelForImageClassification
import torch
from torchvision import transforms
import sys
import torchvision
from torch.profiler import profile, ProfilerActivity

# ImageNet标准数据预处理管道
data_transforms = {
    'predict': transforms.Compose([
        transforms.Resize(256),                                                    # 调整图像大小到256x256
        transforms.CenterCrop(224),                                               # 中心裁剪到224x224
        transforms.ToTensor(),                                                    # 转换为张量
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),  # ImageNet标准化
    ])
}

# GPU设备配置
device = torch.device("cuda")
starter = torch.cuda.Event(enable_timing=True)  # CUDA事件，用于精确计时
ender = torch.cuda.Event(enable_timing=True)
torch.cuda.Event(enable_timing=True)
current = torch.cuda.current_device()           # 当前GPU设备ID
gpuname = torch.cuda.get_device_name(current)   # GPU名称

# 命令行参数解析
skiprows = int(sys.argv[1])  # 跳过的模型数量（用于分批处理）
bsinput = int(sys.argv[2])   # 批处理大小
# 读取模型列表文件并进行性能测试
with open('./modelinfo.txt') as f:
    # 跳过指定数量的模型（用于分批处理）
    while skiprows != 0:
        f.readline()
        skiprows -= 1

    k = 0
    for line in f.readlines():
        k = k + 1
        if k > 100:  # 每批最多处理100个模型
            break
        try:
            timelist = []
            modelid = line.strip()  # 获取模型ID

            # 加载HuggingFace预训练模型
            # feature_extractor = AutoFeatureExtractor.from_pretrained(modelid)
            model = AutoModelForImageClassification.from_pretrained(modelid, use_safetensors=True)

            # 配置批处理大小
            bslist = [bsinput]
            for bsitem in bslist:
                bs = bsitem        # 批大小
                imagenum = bsitem  # 图像数量（与批大小相同）

                # 准备ImageNet验证集数据
                dataset = {'predict': torchvision.datasets.ImageFolder("../ILSVRC2012_img_val", data_transforms['predict'])}
                dataset_subset = dataset['predict']
                dataset_subset = torch.utils.data.Subset(dataset['predict'], range(imagenum))  # 选择指定数量的图像
                dataloader = {'predict': torch.utils.data.DataLoader(
                    dataset_subset,
                    batch_size=bs,
                    shuffle=False,
                    num_workers=8,      # 多线程数据加载
                    pin_memory=True     # 固定内存，加速GPU传输
                )}

                # 对每个数据批次进行性能测试
                for input_batch, labels in dataloader['predict']:
                    model.eval()  # 设置为评估模式
                    total = 0

                    # 将数据和模型移动到GPU
                    if torch.cuda.is_available():
                        input_batch = input_batch.to('cuda')
                        model.to('cuda')

                    with torch.no_grad():  # 禁用梯度计算，节省内存和计算
                        # 进行51次推理：前20次预热，21-50次计时，第51次profiling
                        for i in range(51):
                            if i == 50:  # 第51次：详细profiling
                                # 使用PyTorch Profiler记录详细的CPU和CUDA活动
                                with profile(
                                    activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
                                    record_shapes=True,  # 记录张量形状信息
                                ) as prof:
                                    output = model(input_batch)
                                    torch.cuda.synchronize()  # 同步GPU，确保所有操作完成

                                # 导出Chrome trace文件，用于后续分析
                                prof.export_chrome_trace("./traceprofiler/" + modelid.replace('/', '&') + ".json")

                            else:
                                if i >= 20:  # 第21-50次：精确计时
                                    torch.cuda.synchronize()
                                    starter = torch.cuda.Event(enable_timing=True)
                                    ender = torch.cuda.Event(enable_timing=True)
                                    starter.record()  # 开始计时
                                    model(input_batch)
                                    ender.record()    # 结束计时
                                    torch.cuda.synchronize()
                                    curr_time = starter.elapsed_time(ender)  # 获取执行时间（毫秒）
                                    total += curr_time
                                else:  # 前20次：预热GPU
                                    model(input_batch)

                    # 计算平均执行时间（30次测量的平均值）
                    timelist.append(total / 30)

            # 输出结果：GPU名称，批大小，模型ID，执行时间
            print(gpuname, ',', bs, ',', modelid, ',', ','.join('%f' % timeinfo for timeinfo in timelist))

        except Exception as e:
            print(e)
            # 忽略加载失败的模型，继续处理下一个
            pass
