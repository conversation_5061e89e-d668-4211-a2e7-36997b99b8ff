import argparse
import logging
from .calc_func import *
import torch
import torch.nn as nn
from torch.nn.modules.conv import _ConvNd
from thop.vision import value
multiply_adds = 1

def count_parameters(m, x, y):
    total_params = 0
    for p in m.parameters():
        total_params += torch.DoubleTensor([p.numel()])
    m.total_params[0] = calculate_parameters(m.parameters())


def zero_ops(m, x, y):
    m.total_ops += calculate_zero_ops()

    


def count_convNd(m: _ConvNd, x, y: torch.Tensor):
    x = x[0]

    kernel_ops = torch.zeros(m.weight.size()[2:]).numel()  # Kw x Kh
    bias_ops = 1 if m.bias is not None else 0

    m.total_ops += calculate_conv2d_flops(
        input_size = list(x.shape),
        output_size = list(y.shape),
        kernel_size = list(m.weight.shape),
        groups = m.groups,
        bias = m.bias
    )
    input_size = list(x.shape),
    output_size = list(y.shape),
    kernel_size = list(m.weight.shape)
    mac=l_prod(l_prod(output_size)) +l_prod(l_prod(input_size)) + l_prod(kernel_size[2:])* (l_prod(input_size)[1] // m.groups)* (l_prod(output_size)[1] // m.groups)
    maccountly= value.get_value('maccountly')
    maccountly +=mac    
    value.set_value('maccountly',maccountly)
    convops= value.get_value('convops')
    convops +=m.total_ops.item()
    value.set_value('convops',convops)
    print(value.get_value('modelidly'),',count_convNd,',m.total_ops.item(),',',list(x.shape),',',list(y.shape),',',list(m.weight.shape))


def count_convNd_ver2(m: _ConvNd, x, y: torch.Tensor):
    x = x[0]

    # N x H x W (exclude Cout)
    output_size = torch.zeros((y.size()[:1] + y.size()[2:])).numel()
    m.total_ops += calculate_conv(m.bias.nelement(), m.weight.nelement(), output_size)
    convops= value.get_value('convops')
    convops +=m.total_ops.item()
    value.set_value('convops',convops)
    print(value.get_value('modelidly'),',count_convNd_ver2,',',',m.total_ops.item(),',',list(x.shape),',',list(y.shape),',',list(m.weight.shape))
    


def count_normalization(m: nn.modules.batchnorm._BatchNorm, x, y):
    # TODO: add test cases
    # https://github.com/Lyken17/pytorch-OpCounter/issues/124
    # y = (x - mean) / sqrt(eps + var) * weight + bias
    x = x[0]
    # bn is by default fused in inference
    flops = calculate_norm(x.numel())
    if (getattr(m, 'affine', False) or getattr(m, 'elementwise_affine', False)):
        flops *= 2
    m.total_ops += flops
    normops= value.get_value('normops')
    normops +=m.total_ops.item()
    value.set_value('normops',normops)
    print(value.get_value('modelidly'),',count_normalization,',m.total_ops.item(),',',list(x.shape),',',list(y.shape),',',l_prod(list(y.shape)))


def count_prelu(m, x, y):
    x = x[0]

    nelements = x.numel()
    if not m.training:
        m.total_ops += calculate_relu(nelements)
    otherops= value.get_value('otherops')
    otherops +=m.total_ops.item()
    value.set_value('otherops',otherops)
    print(value.get_value('modelidly'),',count_prelu,',m.total_ops.item(),',',list(x.shape),',',list(y.shape))
    

def count_relu(m, x, y):
    x = x[0]

    nelements = x.numel()

    m.total_ops += calculate_relu_flops(list(x.shape))
    otherops= value.get_value('otherops')
    otherops +=m.total_ops.item()
    value.set_value('otherops',otherops)
    print(value.get_value('modelidly'),',count_relu,',m.total_ops.item(),',',list(x.shape),',',list(y.shape))
    


def count_softmax(m, x, y):
    x = x[0]
    nfeatures = x.size()[m.dim]
    batch_size = x.numel() // nfeatures

    m.total_ops += calculate_softmax(batch_size, nfeatures)
    otherops= value.get_value('otherops')
    otherops +=m.total_ops.item()
    value.set_value('otherops',otherops)
    print(value.get_value('modelidly'),',count_softmax,',m.total_ops.item(),',',list(x.shape),',',list(y.shape),',',list(m.weight.shape))
    



def count_avgpool(m, x, y):
    num_elements = y.numel()
    m.total_ops += calculate_avgpool(num_elements)
    poolops= value.get_value('poolops')
    poolops +=m.total_ops.item()
    value.set_value('poolops',poolops)
    print(value.get_value('modelidly'),',count_avgpool,',m.total_ops.item(),',',list(x[0].shape),',',list(y.shape),',',num_elements)
    


def count_adap_avgpool(m, x, y):
    kernel = torch.div(
        torch.DoubleTensor([*(x[0].shape[2:])]), 
        torch.DoubleTensor([*(y.shape[2:])])
    )
    total_add = torch.prod(kernel)
    num_elements = y.numel()
    m.total_ops += calculate_adaptive_avg(total_add, num_elements)
    poolops= value.get_value('poolops')
    poolops +=m.total_ops.item()
    value.set_value('poolops',poolops)
    print(value.get_value('modelidly'),',count_adap_avgpool,',m.total_ops.item(),',',list(x[0].shape),',',list(y.shape),',',num_elements)


def count_maxpool(m, x, y):
    num_elements = y.numel()
    m.total_ops += calculate_avgpool(num_elements)
    poolops= value.get_value('poolops')
    poolops +=m.total_ops.item()
    value.set_value('poolops',poolops)
    print(value.get_value('modelidly'),',count_maxpool,',m.total_ops.item(),',',list(x[0].shape),',',list(y.shape),',',num_elements)

    
     
def count_adap_maxpool(m, x, y):
    kernel = torch.div(
        torch.DoubleTensor([*(x[0].shape[2:])]), 
        torch.DoubleTensor([*(y.shape[2:])])
    )
    total_add = torch.prod(kernel)
    num_elements = y.numel()
    m.total_ops += calculate_adaptive_avg(total_add, num_elements)
    poolops= value.get_value('poolops')
    poolops +=m.total_ops.item()
    value.set_value('poolops',poolops)
    print(value.get_value('modelidly'),',count_adap_maxpool,',m.total_ops.item(),',',list(x[0].shape),',',list(y.shape),',',num_elements)


# TODO: verify the accuracy
def count_upsample(m, x, y):
    if m.mode not in (
        "nearest",
        "linear",
        "bilinear",
        "bicubic",
    ):  # "trilinear"
        logging.warning("mode %s is not implemented yet, take it a zero op" % m.mode)
        m.total_ops += 0
    else:
        x = x[0]
        m.total_ops += calculate_upsample(m.mode, y.nelement())
    otherops= value.get_value('otherops')
    otherops +=m.total_ops.item()
    value.set_value('otherops',otherops)
    print(value.get_value('modelidly'),',count_upsample,',m.total_ops.item(),',',list(x[0].shape),',',list(y.shape),',',y.nelement())

    


# nn.Linear
def count_linear(m, x, y):
    # per output element
    total_mul = m.in_features
    # total_add = m.in_features - 1
    # total_add += 1 if m.bias is not None else 0
    num_elements = y.numel()
    m.total_ops += calculate_linear(total_mul, num_elements)
    mac=m.in_features+m.out_features+m.in_features*m.out_features
    maccountly= value.get_value('maccountly')
    maccountly +=mac
    value.set_value('maccountly',maccountly)
    fccops= value.get_value('fccops')
    fccops +=m.total_ops.item()
    value.set_value('fccops',fccops)
    print(value.get_value('modelidly'),',count_linear,',m.total_ops.item(),',',mac,',',m.in_features,',',m.out_features,',',num_elements)

    

def count_gelu(m, x, y):
    x = x[0]
    m.total_ops += x.numel()
    
    print(value.get_value('modelidly'),',count_gelu,',m.total_ops.item(),',',mac,',',',',',',x.numel())

    
def count_silu(m, x, y):
    x = x[0]
    m.total_ops += x.numel()
    print(value.get_value('modelidly'),',count_silu,',m.total_ops.item(),',',mac,',',',',',',x.numel())

def count_tanh(m, x, y):
    x = x[0]
    m.total_ops += x.numel()
    print(value.get_value('modelidly'),',count_silu,',m.total_ops.item(),',',mac,',',',',',',x.numel())

