import sys
import pandas as pd


def l_prod(in_list):
    res = 1
    for _ in in_list:
        res *= _
    return res


def mapkernelops(datafile, flopsfile):
    print('modelid,layerid,cpueventname,cudatime,cudatimenooverlap,',
          'inputdims,inputproducts,inputsize,kernelduration,blocksperSM,',
          'warpsperSM,stream,grid,block,kernelname,outputdims,outputproducts,outputsize,ops,infeatures,outfeatures')

    df = pd.read_csv(datafile,
                     usecols=['modelid', 'layerid', 'cpueventname', 'cudatime', 'cudatimenooverlap',
                              'inputdims', 'inputproducts', 'inputsize', 'kernelduration',
                              'blocksperSM', 'warpsperSM', 'stream', 'grid', 'block', 'kernelname'],
                     dtype={'modelid': str, 'kernelname': str}
                     )
    dfmodelid = df['modelid'].unique()
    df['outputdims'] = None
    df['outputproducts'] = None
    df['outputsize'] = None
    df['ops'] = None
    df['infeatures'] = None
    df['outfeatures'] = None
    dfmodel = pd.DataFrame()
    tracename = ''
    with open(flopsfile) as f:
        for line in f.readlines():
            flopsinfo = line.strip()
            flopsinfo = flopsinfo.split(',')
            a = flopsinfo
            if tracename != flopsinfo[0]:
                if (flopsinfo[0].replace('/', '&').replace(' ', '') + '.json ' in dfmodelid):
                    for index1, row1 in dfmodel.iterrows():
                        print(row1['modelid'], ',', row1['layerid'], ',', row1['cpueventname'], ',', row1['cudatime'],
                              ',', row1['cudatimenooverlap'], ',',
                              row1['inputdims'], ',', row1['inputproducts'], ',', row1['inputsize'], ',',
                              row1['kernelduration'], ',',
                              row1['blocksperSM'], ',', row1['warpsperSM'], ',', row1['stream'], ',', row1['grid'], ',',
                              row1['block'], ',', row1['kernelname'], ',',
                              row1['outputdims'], ',', row1['outputproducts'], ',', row1['outputsize'], ',',
                              row1['ops'], ',', row1['infeatures'], ',', row1['outfeatures'])
                    dfmodel = df[(df['modelid'] == flopsinfo[0].replace('/', '&').replace(' ', '') + '.json ')]
            tracename = flopsinfo[0]
            for index2, row2 in dfmodel.iterrows():
                if (dfmodel.loc[index2, 'ops'] == None):
                    if a[1].strip() == 'count_convNd' and row2['cpueventname'].strip() == 'aten::conv2d':
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'outputdims'] = a[4]
                            outputsize = a[4].replace('[', '').replace(']', '').split(';')
                            outputproduct = l_prod(outputsize)
                            dfmodel.loc[index3, 'outputproducts'] = outputproduct
                            dfmodel.loc[index3, 'outputsize'] = str(outputsize).replace(',', ';')
                            dfmodel.loc[index3, 'ops'] = a[2]
                        break
                    elif a[1].strip() == 'count_normalization' and (
                            row2['cpueventname'].strip() == 'aten::batch_norm' or row2[
                        'cpueventname'].strip() == 'aten::layer_norm'):
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'outputdims'] = a[4]
                            outputsize = a[4].replace('[', '').replace(']', '').split(';')
                            outputproduct = l_prod(outputsize)
                            dfmodel.loc[index3, 'outputproducts'] = outputproduct
                            dfmodel.loc[index3, 'outputsize'] = str(outputsize).replace(',', ';')
                            dfmodel.loc[index3, 'ops'] = a[2]
                        break
                    elif (a[1].strip() == 'count_prelu' or a[1].strip() == 'count_relu') and (
                            row2['cpueventname'].strip() == 'aten::relu' or row2[
                        'cpueventname'].strip() == 'aten::relu_'):
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'outputdims'] = a[4]
                            outputsize = a[4].replace('[', '').replace(']', '').split(';')
                            outputproduct = l_prod(outputsize)
                            dfmodel.loc[index3, 'outputproducts'] = outputproduct
                            dfmodel.loc[index3, 'outputsize'] = str(outputsize).replace(',', ';')
                            dfmodel.loc[index3, 'ops'] = a[2]
                        break
                    elif (a[1].strip() == 'count_avgpool' and row2['cpueventname'].strip() == 'aten::avg_pool2d') \
                            or (a[1].strip() == 'count_adap_avgpool' and (
                            row2['cpueventname'].strip() == 'aten::adaptive_avg_pool2d' or row2[
                        'cpueventname'].strip() == 'aten::adaptive_avg_pool1d')) \
                            or (a[1].strip() == 'count_maxpool' and row2['cpueventname'].strip() == 'aten::max_pool2d ') \
                            or (a[1].strip() == 'count_adap_maxpool' and row2[
                        'cpueventname'].strip() == 'aten::adaptive_max_pool2d'):
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'outputdims'] = a[4]
                            outputsize = a[4].replace('[', '').replace(']', '').split(';')
                            outputproduct = l_prod(outputsize)
                            dfmodel.loc[index3, 'outputproducts'] = outputproduct
                            dfmodel.loc[index3, 'outputsize'] = str(outputsize).replace(',', ';')
                            dfmodel.loc[index3, 'ops'] = a[2]
                        break
                    elif a[1].strip() == 'count_linear' and row2['cpueventname'].strip() == 'aten::linear':
                        dflayer = dfmodel[(dfmodel['layerid'] == row2['layerid'])]
                        for index3, row3 in dflayer.iterrows():
                            dfmodel.loc[index3, 'infeatures'] = a[4]
                            dfmodel.loc[index3, 'outfeatures'] = a[5]
                            dfmodel.loc[index3, 'ops'] = a[2]
                        break
                    else:
                        pass


if __name__ == "__main__":
    datafile = sys.argv[1]
    flopsfile = sys.argv[2]
    mapkernelops(datafile, flopsfile)
