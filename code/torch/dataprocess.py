import json
import os


class Kernellist:
    def __init__(self):
        self.name = ''
        self.id = None
        self.duration = None
        self.starttime = None
        self.endtime = None
        self.correlationid = None
        self.registersperthread = None
        self.sharedmemory = None
        self.blocksperSM = None
        self.warpsperSM = None
        self.grid = ''
        self.block = ''
        self.stream = None


class Operatorlist:
    def __init__(self):
        self.name = ''
        self.id = None
        self.duration = None
        self.starttime = None
        self.endtime = None
        self.correlationid = []
        self.cudaevents = []
        self.inputdims = ''

def l_prod(in_obj):
    """计算（可能嵌套的）列表/元组/可转整数字符串的乘积。"""
    res = 1
    def _acc(x):
        nonlocal res
        if isinstance(x, (list, tuple)):
            for v in x:
                _acc(v)
        else:
            try:
                res *= int(x)
            except Exception:
                pass
    _acc(in_obj)
    return res


def dataprocess():
    path = './traceprofiler/'
    files = os.listdir(path)
    print(
        'modelid,layerid,cpueventname,cudatime,cudatimenooverlap,inputdims,'
        'inputproducts,inputsize,kernelduration,blocksperSM,'
        'warpsperSM,stream,grid,block,kernelname')
    fileid = 0
    for file in files:
        fileid += 1
        if fileid >= 100:
            break
        with open(path + file, "r") as f:
            json_trace = json.load(f)
        cpuevents = []
        profilerstarttime = 0
        for event in json_trace['traceEvents']:
            if (event.get('cat', '').lower() == 'cpu_op') or (event.get('cat', '').lower() == 'operator') and event.get(
                    'ph', '').lower() == 'x':
                dur = event['dur']
                ts = event['ts']
                te = ts + dur
                popitem = []
                aoperator = Operatorlist()
                aoperator.name = event['name']
                aoperator.duration = dur
                aoperator.starttime = ts
                aoperator.endtime = te
                aoperator.inputdims = event['args'].get('Input Dims', [])
                cpuevents.append(aoperator)

                for cpueventsitem in cpuevents:
                    if (te <= cpueventsitem.endtime and ts > cpueventsitem.starttime) or (
                            te < cpueventsitem.endtime and ts >= cpueventsitem.starttime) \
                            or (
                            te == cpueventsitem.endtime and ts == cpueventsitem.starttime and aoperator.name != cpueventsitem.name):  # 040223, ts > to ts >=
                        popitem.append(aoperator)
                    elif te >= cpueventsitem.endtime and ts < cpueventsitem.starttime:
                        popitem.append(cpueventsitem)

                for item in popitem:
                    if item in cpuevents:
                        cpuevents.remove(item)


            elif (event.get('cat', '').lower() == 'cuda_runtime') or (
                    event.get('cat', '').lower() == 'runtime') and event.get('ph', '').lower() == 'x' and event.get(
                'name', '').lower() == 'cudalaunchkernel':
                dur = event['dur']
                ts = event['ts']
                te = ts + dur
                correlationid = event['args']["correlation"]
                for cpueventsitem in cpuevents:
                    if cpueventsitem.endtime > te and cpueventsitem.starttime < ts:
                        cpueventsitem.correlationid.append(correlationid)
            elif event.get('name', '') == 'Iteration Start: PyTorch Profiler':
                profilerstarttime = event.get('ts')

        for event in json_trace['traceEvents']:
            if event.get('cat', '').lower() == 'kernel' and event.get('ph', '').lower() == 'x':
                correlationid = event['args']["correlation"]
                dur = event['dur']
                ts = event['ts']
                te = ts + dur
                cudaevents = []
                for cpueventsitem in cpuevents:
                    if correlationid in cpueventsitem.correlationid:
                        akernel = Kernellist()
                        akernel.name = event['name']
                        akernel.duration = dur
                        akernel.starttime = ts
                        akernel.endtime = te
                        akernel.correlationid = correlationid
                        akernel.registersperthread = event['args']['registers per thread']
                        akernel.sharedmemory = event['args']['shared memory']
                        akernel.blocksperSM = ''  # event['args']['blocks per SM']
                        akernel.warpsperSM = event['args']['warps per SM']
                        akernel.grid = event['args']['grid']
                        akernel.block = event['args']['block']
                        akernel.stream = event['args']['stream']
                        cudaevents.append(akernel)
                        cpueventsitem.cudaevents.append(cudaevents)
        layerid = 0
        for cpueventsitem in cpuevents:
            layerid += 1
            # 归一化输入形状，防止 [[N,C,H,W]] 这类多重嵌套导致乘积失败
            inputshape = []
            dims = cpueventsitem.inputdims
            if isinstance(dims, (list, tuple)) and len(dims) > 0:
                inputshape = dims[0]
                while isinstance(inputshape, (list, tuple)) and len(inputshape) == 1 and isinstance(inputshape[0], (list, tuple)):
                    inputshape = inputshape[0]

            inputproducts = l_prod(inputshape)
            inputsize = inputshape
            if cpueventsitem.name == 'aten::conv2d':
                bias = cpueventsitem.inputdims[2]
                kernelsize = cpueventsitem.inputdims[1][2:]
            else:
                bias = 0
                kernelsize = 0
            cudatime = 0
            mincuda = 0
            maxcuda = 0
            cudatimenooverlap = 0
            for cudaeventsitem in cpueventsitem.cudaevents:
                for item in cudaeventsitem:
                    if mincuda == 0:
                        mincuda = item.starttime - profilerstarttime
                    elif mincuda > item.starttime - profilerstarttime:
                        mincuda = item.starttime - profilerstarttime
                    if maxcuda < item.endtime - profilerstarttime:
                        maxcuda = item.endtime - profilerstarttime
                    cudatimenooverlap += item.endtime - item.starttime
            cudatime = maxcuda - mincuda

            for cudaeventsitem in cpueventsitem.cudaevents:
                for item in cudaeventsitem:
                    print(file, ',', layerid, ',', cpueventsitem.name, ',', cudatime, ',', cudatimenooverlap, ',',
                          str(cpueventsitem.inputdims).replace(' ', '').replace(',', ';')
                          , ',', inputproducts, ',', str(inputsize).replace(',', ';'), ',',
                          item.duration, ',', item.blocksperSM, ',',
                          item.warpsperSM, ',', item.stream, ',', str(item.grid).replace(',', ';'), ',',
                          str(item.block).replace(',', ';'), ',',
                          item.name.replace(',', ';'))

if __name__ == "__main__":
    dataprocess()
