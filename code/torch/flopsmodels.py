import torch
import time
import torchvision
import sys
from torchvision import transforms
import torchvision.models as models
import torch.nn as nn
import math
from thop import profile
import copy
from torchvision.models import (
    VGG11_Weights, VGG13_Weights, VGG16_Weights, VGG19_Weights,
    ResNet18_Weights, ResNet34_Weights, ResNet50_Weights, ResNet101_Weights, ResNet152_Weights,
    AlexNet_Weights,
    SqueezeNet1_1_Weights, SqueezeNet1_0_Weights,
    DenseNet161_Weights, DenseNet169_Weights, DenseNet121_Weights, DenseNet201_Weights,
    Inception_V3_Weights, GoogLeNet_Weights,
    ShuffleNet_V2_X1_0_Weights, ShuffleNet_V2_X0_5_Weights,
    MobileNet_V2_Weights, MobileNet_V3_Large_Weights, MobileNet_V3_Small_Weights,
    ResNeXt50_32X4D_Weights, ResNeXt101_32X8D_Weights,
    Wide_ResNet50_2_Weights, Wide_ResNet101_2_Weights,
    MNASNet1_0_Weights, MNASNet0_5_Weights,
)

bsinput = int(sys.argv[1])
listmodel = []


def conv3x3(in_planes, out_planes, stride=1):
    """3x3 convolution with padding"""
    return nn.Conv2d(in_planes, out_planes, kernel_size=3, stride=stride,
                     padding=1, bias=False)


class BasicBlock(nn.Module):
    expansion = 1

    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(BasicBlock, self).__init__()
        self.conv1 = conv3x3(inplanes, planes, stride)
        self.bn1 = nn.BatchNorm2d(planes)
        self.relu = nn.ReLU(inplace=True)
        self.conv2 = conv3x3(planes, planes)
        self.bn2 = nn.BatchNorm2d(planes)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = self.relu(out)

        return out


class Bottleneck(nn.Module):
    expansion = 4

    def __init__(self, inplanes, planes, stride=1, downsample=None):
        super(Bottleneck, self).__init__()
        self.conv1 = nn.Conv2d(inplanes, planes, kernel_size=1, bias=False)
        self.bn1 = nn.BatchNorm2d(planes)
        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, stride=stride,
                               padding=1, bias=False)
        self.bn2 = nn.BatchNorm2d(planes)
        self.conv3 = nn.Conv2d(planes, planes * 4, kernel_size=1, bias=False)
        self.bn3 = nn.BatchNorm2d(planes * 4)
        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride

    def forward(self, x):
        residual = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        out = self.conv2(out)
        out = self.bn2(out)
        out = self.relu(out)

        out = self.conv3(out)
        out = self.bn3(out)

        if self.downsample is not None:
            residual = self.downsample(x)

        out += residual
        out = self.relu(out)

        return out


class CNN(nn.Module):
    def __init__(self, block, layers, num_classes=1000):
        self.inplanes = 64
        super(CNN, self).__init__()
        self.conv1 = nn.Conv2d(3, 64, kernel_size=7, stride=2, padding=3,
                               bias=False)
        self.bn1 = nn.BatchNorm2d(64)
        self.relu = nn.ReLU(inplace=True)
        self.maxpool = nn.MaxPool2d(kernel_size=3, stride=2, padding=1)
        self.layer1 = self._make_layer(block, 64, layers[0])
        self.layer2 = self._make_layer(block, 128, layers[1], stride=2)
        self.layer3 = self._make_layer(block, 256, layers[2], stride=2)
        self.layer4 = self._make_layer(block, layers[4], layers[3], stride=2)  # 512
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.fc = nn.Linear(layers[4] * block.expansion, num_classes)  # 512

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

    def _make_layer(self, block, planes, blocks, stride=1):
        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                nn.Conv2d(self.inplanes, planes * block.expansion,
                          kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, stride, downsample))
        self.inplanes = planes * block.expansion
        for i in range(1, blocks):
            layers.append(block(self.inplanes, planes))

        return nn.Sequential(*layers)

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.maxpool(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)
        x = self.layer4(x)

        x = self.avgpool(x)
        x = x.view(x.size(0), -1)
        x = self.fc(x)

        return x


base_vgg16 = models.vgg16(weights=VGG16_Weights.IMAGENET1K_V1)

vgg4 = copy.deepcopy(base_vgg16)
vgg4.features = nn.Sequential(*list(vgg4.features.children())[0:2], *list(vgg4.features.children())[4:5])
vgg4.classifier = nn.Sequential(nn.Linear(64 * 7 * 7, 4096), *list(vgg4.classifier.children())[1:7])

vgg5 = copy.deepcopy(base_vgg16)
vgg5.features = nn.Sequential(*list(vgg5.features.children())[0:2], *list(vgg5.features.children())[4:7],
                              *list(vgg5.features.children())[9:10])
vgg5.classifier = nn.Sequential(nn.Linear(128 * 7 * 7, 4096), *list(vgg5.classifier.children())[1:7])

vgg6 = copy.deepcopy(base_vgg16)
vgg6.features = nn.Sequential(*list(vgg6.features.children())[0:2], *list(vgg6.features.children())[4:7],
                              *list(vgg6.features.children())[9:12],
                              *list(vgg6.features.children())[16:17])
vgg6.classifier = nn.Sequential(nn.Linear(256 * 7 * 7, 4096), *list(vgg6.classifier.children())[1:7])

vgg7 = copy.deepcopy(base_vgg16)
vgg7.features = nn.Sequential(*list(vgg7.features.children())[0:2], *list(vgg7.features.children())[4:7],
                              *list(vgg7.features.children())[9:12],
                              *list(vgg7.features.children())[16:19], *list(vgg7.features.children())[23:24])

vgg8 = copy.deepcopy(base_vgg16)
vgg8.features = nn.Sequential(*list(vgg8.features.children())[0:2], *list(vgg8.features.children())[4:7],
                              *list(vgg8.features.children())[9:12],
                              *list(vgg8.features.children())[16:19], *list(vgg8.features.children())[23:26],
                              *list(vgg8.features.children())[30:31])

vgg9 = copy.deepcopy(base_vgg16)
vgg9.features = nn.Sequential(*list(vgg9.features.children())[0:2], *list(vgg9.features.children())[4:7],
                              *list(vgg9.features.children())[9:12],
                              *list(vgg9.features.children())[16:19], *list(vgg9.features.children())[23:28],
                              *list(vgg9.features.children())[30:31])

vgg10 = copy.deepcopy(base_vgg16)
vgg10.features = nn.Sequential(*list(vgg10.features.children())[0:2], *list(vgg10.features.children())[4:7],
                               *list(vgg10.features.children())[9:12],
                               *list(vgg10.features.children())[16:21], *list(vgg10.features.children())[23:28],
                               *list(vgg10.features.children())[30:31])

vgg11 = models.vgg11(weights=VGG11_Weights.DEFAULT)
vgg13 = models.vgg13(weights=VGG13_Weights.DEFAULT)
vgg16 = models.vgg16(weights=VGG16_Weights.DEFAULT)
vgg19 = models.vgg19(weights=VGG19_Weights.DEFAULT)

resnet10 = CNN(BasicBlock, [1, 1, 1, 1, 512])
resnet18 = models.resnet18(weights=ResNet18_Weights.DEFAULT)
resnet26 = CNN(BasicBlock, [3, 3, 3, 3, 512])
resnet34 = models.resnet34(weights=ResNet34_Weights.DEFAULT)
resnet44 = CNN(Bottleneck, [3, 4, 4, 3, 512])
resnet50 = models.resnet50(weights=ResNet50_Weights.DEFAULT)
resnet62 = CNN(Bottleneck, [3, 4, 10, 3, 512])
resnet77 = CNN(Bottleneck, [3, 4, 15, 3, 512])
resnet101 = models.resnet101(weights=ResNet101_Weights.DEFAULT)
resnet116 = CNN(Bottleneck, [3, 4, 28, 3, 512])
resnet122 = CNN(Bottleneck, [3, 6, 28, 3, 512])
resnet134 = CNN(Bottleneck, [3, 8, 30, 3, 512])
resnet152 = models.resnet152(weights=ResNet152_Weights.DEFAULT)

resnet10g1 = CNN(BasicBlock, [1, 1, 1, 1, 256])
resnet18g1 = CNN(BasicBlock, [2, 2, 2, 2, 256])
resnet26g1 = CNN(BasicBlock, [3, 3, 3, 3, 256])
resnet34g1 = CNN(BasicBlock, [3, 4, 6, 3, 256])
resnet44g1 = CNN(Bottleneck, [3, 4, 4, 3, 256])
resnet50g1 = CNN(Bottleneck, [3, 4, 6, 3, 256])
resnet62g1 = CNN(Bottleneck, [3, 4, 10, 3, 256])
resnet77g1 = CNN(Bottleneck, [3, 4, 15, 3, 256])
resnet101g1 = CNN(Bottleneck, [3, 4, 23, 3, 256])
resnet116g1 = CNN(Bottleneck, [3, 4, 28, 3, 256])
resnet122g1 = CNN(Bottleneck, [3, 6, 28, 3, 256])
resnet134g1 = CNN(Bottleneck, [3, 8, 30, 3, 256])
resnet152g1 = CNN(Bottleneck, [3, 8, 36, 3, 256])

resnet44g2 = CNN(BasicBlock, [3, 4, 4, 3, 512])
resnet50g2 = CNN(BasicBlock, [3, 4, 6, 3, 512])
resnet62g2 = CNN(BasicBlock, [3, 4, 10, 3, 512])
resnet77g2 = CNN(BasicBlock, [3, 4, 15, 3, 512])
resnet101g2 = CNN(BasicBlock, [3, 4, 23, 3, 512])
resnet116g2 = CNN(BasicBlock, [3, 4, 28, 3, 512])
resnet122g2 = CNN(BasicBlock, [3, 6, 28, 3, 512])
resnet134g2 = CNN(BasicBlock, [3, 8, 30, 3, 512])
resnet152g2 = CNN(BasicBlock, [3, 8, 36, 3, 512])

alexnet = models.alexnet(weights=AlexNet_Weights.DEFAULT)
squeezenet1_1 = models.squeezenet1_1(weights=SqueezeNet1_1_Weights.DEFAULT)
squeezenet1_0 = models.squeezenet1_0(weights=SqueezeNet1_0_Weights.DEFAULT)
densenet161 = models.densenet161(weights=DenseNet161_Weights.DEFAULT)
densenet169 = models.densenet169(weights=DenseNet169_Weights.DEFAULT)
densenet121 = models.densenet121(weights=DenseNet121_Weights.DEFAULT)
densenet201 = models.densenet201(weights=DenseNet201_Weights.DEFAULT)
inception = models.inception_v3(weights=Inception_V3_Weights.DEFAULT)
googlenet = models.googlenet(weights=GoogLeNet_Weights.DEFAULT)
shufflenet1_0 = models.shufflenet_v2_x1_0(weights=ShuffleNet_V2_X1_0_Weights.DEFAULT)
shufflenet0_5 = models.shufflenet_v2_x0_5(weights=ShuffleNet_V2_X0_5_Weights.DEFAULT)
mobilenetv2 = models.mobilenet_v2(weights=MobileNet_V2_Weights.DEFAULT)
mobilenet_v3_large = models.mobilenet_v3_large(weights=MobileNet_V3_Large_Weights.DEFAULT)
mobilenet_v3_small = models.mobilenet_v3_small(weights=MobileNet_V3_Small_Weights.DEFAULT)
resnext50_32x4d = models.resnext50_32x4d(weights=ResNeXt50_32X4D_Weights.DEFAULT)
resnext101_32x8d = models.resnext101_32x8d(weights=ResNeXt101_32X8D_Weights.DEFAULT)
wide_resnet50_2 = models.wide_resnet50_2(weights=Wide_ResNet50_2_Weights.DEFAULT)
wide_resnet101_2 = models.wide_resnet101_2(weights=Wide_ResNet101_2_Weights.DEFAULT)
mnasnet1_0 = models.mnasnet1_0(weights=MNASNet1_0_Weights.DEFAULT)
mnasnet0_5 = models.mnasnet0_5(weights=MNASNet0_5_Weights.DEFAULT)

data_transforms = {
    'predict': transforms.Compose([
        transforms.Resize(256),
        transforms.CenterCrop(224),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
    ])
}

listmodel = [vgg4, vgg5, vgg6, vgg7, vgg8, vgg9, vgg10, vgg11, vgg13, vgg16, vgg19, resnet10, resnet18, resnet26,
             resnet34, resnet44, resnet50,
             resnet62, resnet77, resnet101, resnet116, resnet122, resnet134,
             resnet152, resnet10g1, resnet18g1, resnet26g1, resnet34g1, resnet44g1, resnet50g1, resnet62g1, resnet77g1,
             resnet101g1,
             resnet116g1, resnet122g1, resnet134g1, resnet152g1, resnet44g2, resnet50g2, resnet62g2, resnet77g2,
             resnet101g2, resnet116g2,
             resnet122g2, resnet134g2, resnet152g2, alexnet, squeezenet1_1,
             squeezenet1_0, densenet161, densenet169, densenet121, densenet201, inception, googlenet, shufflenet1_0,
             shufflenet0_5, mobilenetv2, mobilenet_v3_large, mobilenet_v3_small, resnext50_32x4d, resnext101_32x8d,
             wide_resnet50_2, wide_resnet101_2, mnasnet1_0, mnasnet0_5]

namelist = ['vgg4', 'vgg5', 'vgg6', 'vgg7', 'vgg8', 'vgg9', 'vgg10', 'vgg11', 'vgg13', 'vgg16', 'vgg19', 'resnet10',
            'resnet18', 'resnet26', 'resnet34', 'resnet44', 'resnet50',
            'resnet62', 'resnet77', 'resnet101', 'resnet116', 'resnet122', 'resnet134',
            'resnet152', 'resnet10g1', 'resnet18g1', 'resnet26g1', 'resnet34g1', 'resnet44g1', 'resnet50g1',
            'resnet62g1', 'resnet77g1', 'resnet101g1',
            'resnet116g1', 'resnet122g1', 'resnet134g1', 'resnet152g1', 'resnet44g2', 'resnet50g2', 'resnet62g2',
            'resnet77g2', 'resnet101g2', 'resnet116g2',
            'resnet122g2', 'resnet134g2', 'resnet152g2', 'alexnet', 'squeezenet1_1',
            'squeezenet1_0', 'densenet161', 'densenet169', 'densenet121', 'densenet201', 'inception', 'googlenet',
            'shufflenet1_0', 'shufflenet0_5', 'mobilenetv2', 'mobilenet_v3_large', 'mobilenet_v3_small',
            'resnext50_32x4d', 'resnext101_32x8d', 'wide_resnet50_2', 'wide_resnet101_2', 'mnasnet1_0', 'mnasnet0_5']
j = 0
for m in listmodel:
    name = namelist[j]
    j = j + 1
    model = m
    input = torch.randn(bsinput, 3, 224, 224)
    flops, params, maccountly, convops, fccops, poolops, normops, otherops = profile(model, name, inputs=(input,))
    print(name, ",", flops, ",", params, ",", maccountly, ",", convops, ",", fccops, ",", poolops, ",", normops, ",",
          otherops)
