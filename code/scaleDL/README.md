# scaleDL（dnnperf）

scaleDL 是一个轻量级框架，用于深度神经网络（DNN）运行时性能预测。它基于“layer 级别建模 + 图神经网络（GNN）校正”的思路，实现了从数据采集、layer 预测器训练，到端到端新模型性能预测的完整流程。

## 核心思路

### 图层库（Layer Bank）
- 为每种算子（Conv、Linear、LayerNorm 等）训练一个回归模型
- 输入：layer 特征（FLOPs、参数量、张量大小、批大小、序列长度等）
- 输出：运行时指标（延迟、显存）

### GNN 校正
- 将整个模型视作图：节点 = layer，边 = 数据依赖
- 用 GNN 学习结构性残差（例如 layer 间的数据流、通信、调度）
- 对 layer bank 的逐层预测进行修正，得到更准确的全模型性能估计

### 端到端预测
- 对一个新模型，先做逐层预测，再通过 GNN 校正，输出总延迟 / 峰值显存

## 项目结构
```
scaleDL/
├─ data/                # 存储采集好的数据
│  ├─ layers.parquet    # 每个 layer 的特征 + 运行时
│  └─ edges.parquet     # layer 之间的依赖关系/特征
│
├─ features/
│  ├─ layer.py          # 提取 layer 的输入维度/参数量/FLOPs/显存等
│  └─ edge.py           # 提取边的张量大小/通信量/依赖关系
│
├─ models/
│  ├─ layer_bank.py     # 每类 layer 的预测器（随机森林/MLP）
│  ├─ gnn.py            # 图神经网络模型（GCN/GAT）
│  └─ predictor.py      # 端到端预测逻辑（结合 layer bank + GNN）
│
├─ train_layers.py      # 训练 layer bank
├─ train_gnn.py         # 训练 GNN 模型
└─ predict.py           # 输入一个新模型，输出预测结果
```

## 数据格式说明

### 1) layers.parquet
| 列名         | 类型   | 说明                                      |
|--------------|--------|-------------------------------------------|
| layer_id     | int    | layer 在模型中的唯一 ID                   |
| layer_type   | str    | 算子类型（Linear, Conv, LayerNorm, …）    |
| flops        | float  | 理论计算量（FLOPs）                       |
| params       | int    | 参数量                                    |
| tensor_bytes | int    | 主要输入/输出张量大小（字节数）           |
| batch_size   | int    | 批大小                                    |
| seq_len      | int    | 序列长度（如 Transformer 用）             |
| latency_us   | float  | 实际运行时延迟（微秒）                    |
| mem_bytes    | int    | 运行时显存占用（字节）                    |

说明：不同 layer 类型可能需要的字段不同，例如 Conv 需要 `kernel_size`, `stride`，而 Linear 不需要。可按需扩展。

### 2) edges.parquet
| 列名            | 类型  | 说明                                      |
|-----------------|-------|-------------------------------------------|
| src_layer_id    | int   | 边的起点 layer                            |
| dst_layer_id    | int   | 边的终点 layer                            |
| tensor_size     | int   | 传递张量的大小（字节）                    |
| is_comm         | bool  | 是否跨设备通信（True/False）              |
| edge_type       | str   | 数据依赖 / 控制依赖 / 通信边              |
| edge_overhead_us| float | 边带来的额外开销（微秒，可选）            |

## 使用流程

### 1) 准备数据
- 在 `data/` 下放置 `layers.parquet` 和 `edges.parquet`。

### 2) 训练 Layer Bank
```bash
python train_layers.py
```

### 3) 训练 GNN
```bash
python train_gnn.py
```

### 4) 预测新模型性能
```bash
python predict.py --model my_model.pt
```

## 设计细节
- Layer Bank：可选用随机森林、XGBoost 或 MLP；特征工程包含 FLOPs/参数量/张量大小/并行度等。
- GNN 校正：支持 GCN/GAT，输入图特征来自 layer/edge 的统计量；输出为对逐层或整模型的误差校正量。
- 推理组合：逐层预测 → 残差校正 → 聚合得到总延迟与峰值显存。

## 未来扩展
- 支持更多 layer 类型（Attention、Embedding 等）
- 引入 few-shot 拟合，自动补齐未见过的 layer
- 多任务预测：延迟、显存、通信开销同时建模
- 多设备与并行拓扑建模（流水线/张量并行）

## 依赖与环境
- Python 3.8+
- PyTorch / PyG（若使用 GNN）
- pandas, numpy, scikit-learn, joblib, tqdm

安装示例：
```bash
pip install torch pandas numpy scikit-learn joblib tqdm
# 可选（使用图神经网络时）
# pip install torch-geometric torch-scatter torch-sparse torch-cluster
```

## 许可证
MIT License