"""模型特征提取：统计叶子层类型数量、总层数与参数量。"""
from features.model_feature import ModelFeature

class ModelFeatureExtractor:
    """遍历模型，统计叶子层分布与参数规模。"""
    def extract_features(self, model):
        """提取模型结构特征。
        
        参数:
            model: PyTorch 模型 (nn.Module)
            
        返回:
            tuple: (features_list, layer_counts, total_params, trainable_params)
                - features_list: [total_layers, layer_Linear_count, layer_Conv2d_count, ...]
                - layer_counts: 各层类型计数字典
                - total_params: 总参数量
                - trainable_params: 可训练参数量
        """
        # 初始化各层类型计数器
        layer_counts = {name: 0 for name in ModelFeature.list_features()}
        total_layers = 0
        
        # 遍历模型的所有模块
        for _, module in model.named_modules():
            # 只统计叶子节点（没有子模块的实际层）
            if len(list(module.children())) == 0:
                # 提取模块类型名称（如 'Linear', 'Conv2d'）
                module_name = str(module).split('(')[0]
                total_layers += 1
                # 统计各层类型数量
                if module_name in layer_counts:
                    layer_counts[module_name] += 1
                else:
                    print(f"[FeatureExtractor] Warning: 未统计到层类型: {module_name}")
        
        # 计算总参数量和可训练参数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        # 构建特征向量：总层数 + 各层类型计数 + 参数量
        features = [total_layers]
        for name in ModelFeature.list_features():
            features.append(layer_counts[name])
        features.extend([total_params, trainable_params])
        
        return features, layer_counts, total_params, trainable_params