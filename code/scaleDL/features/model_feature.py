"""模型层类型枚举，用于统计网络结构特征。"""
from enum import Enum
class ModelFeature(Enum):
    Linear = 1
    Conv2d = 2
    Conv1d = 3
    Dropout = 4
    ReLU = 5
    Sigmoid = 6
    Tanh = 7
    Softmax = 8
    LayerNorm = 9
    NonDynamicallyQuantizableLinear = 10
    Embedding = 11
    GELUActivation = 12

    def __str__(self): return self.name
    @staticmethod
    def list_features(): return [f.name for f in ModelFeature]