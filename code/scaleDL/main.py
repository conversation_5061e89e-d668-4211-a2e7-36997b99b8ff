"""命令入口：采集 → 训练 → 预测 的最小流程。"""
import argparse, os
import pandas as pd
from benchmark.collector import collect_training_data, collect_inference_data
from models.simple_model import SimpleModel
from models.vit import ViT
from models.bert import BERT
from predictor.random_forest_predictor import RandomForestPredictor
import torch

def get_hyperparameter_space(model_cls):
    """根据模型类型返回对应的超参数搜索空间"""
    if model_cls == SimpleModel:
        return {
            'batch_size': [8, 16, 32],
            'seq_len': [64, 128, 256],
            'hidden_size': [128, 256, 512],
            'num_layers': [2, 4],
            'num_heads': [4, 8],
            'dropout': [0.0, 0.1],
        }
    elif model_cls == ViT:
        return {
            'batch_size': [8, 16, 32],
            'image_size': [224, 384],  # ViT 特有
            'patch_size': [16, 32],    # ViT 特有
            'hidden_size': [768, 1024], # ViT 通常更大
            'num_layers': [6, 12],     # 对应 num_hidden_layers
            'num_heads': [8, 16],      # 对应 num_attention_heads
            'dropout': [0.0, 0.1],     # 对应 hidden_dropout_prob
        }
    elif model_cls == BERT:
        return {
            'batch_size': [8, 16],
            'seq_len': [64, 128, 256],  
            'hidden_size': [256, 512],
            'num_layers': [4, 8],
            'num_heads': [4, 8],
            'dropout': [0.0, 0.1],
        }
    else:
        raise ValueError(f"[Main] Error: get_hyperparameter_space 不支持的模型类型: {model_cls}")

def main():
    """解析子命令并执行对应流程。"""
    parser = argparse.ArgumentParser()
    sub = parser.add_subparsers(dest="cmd")

    # collect：新增 --model 与 --pattern，自动保存到 data/{model}/{pattern}.csv
    p_collect = sub.add_parser("collect")
    p_collect.add_argument("--model", choices=["simple","vit","bert"], default="simple", help="选择模型类型")
    p_collect.add_argument("--pattern", choices=["training","inference"], default="training", help="采集模式")
    p_collect.add_argument("--out", default=None, help="输出CSV路径，默认 data/{model}/collect/{pattern}.csv")

    # train：加入 --predictor，并默认读取 data/{model}/{pattern}.csv
    p_train = sub.add_parser("train")
    p_train.add_argument("--model", choices=["simple","vit","bert"], default="simple", help="用于推断默认CSV路径")
    p_train.add_argument("--pattern", choices=["training","inference"], default="training", help="用于推断默认CSV路径")
    p_train.add_argument("--csv", default=None, help="训练数据CSV；默认 data/{model}/collect/{pattern}.csv")
    p_train.add_argument("--target", default="training_time_ms", help="目标列名")
    p_train.add_argument("--predictor", choices=["random_forest"], default="random_forest", help="预测器类型")
    p_train.add_argument("--out_dir", default=None, help="模型数据保存路径；默认 data/{args.model}/predict/{args.target}/{args.predictor}")

    # predict：同样参数化 predictor
    p_predict = sub.add_parser("predict")
    p_predict.add_argument("--predictor", choices=["random_forest"], default="random_forest", help="预测器类型")
    p_predict.add_argument("--model_file", required=True, help="已保存的模型文件路径（.joblib）")
    p_predict.add_argument("--params", nargs="+", required=True, help="k=v 形式的特征输入")

    args = parser.parse_args()

    if args.cmd == "collect":
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model_map = {"simple": SimpleModel, "vit": ViT, "bert": BERT}
        model_cls = model_map[args.model]
        space = get_hyperparameter_space(model_cls)
        out_path = args.out or f"data/{args.model}/collect/{args.pattern}.csv"
        os.makedirs(os.path.dirname(out_path), exist_ok=True)

        if args.pattern == "training":
            df = collect_training_data(model_cls, space, device, num_epochs=5)
        else:
            df = collect_inference_data(model_cls, space, device, num_iter=5)

        df.to_csv(out_path, index=False)
        print(f"saved -> {out_path}")

    elif args.cmd == "train":
        # 默认路径：CSV 和 输出目录
        csv_path = args.csv or f"data/{args.model}/collect/{args.pattern}.csv"
        out_dir = args.out_dir or f"data/{args.model}/predict/{args.target}"
        os.makedirs(out_dir, exist_ok=True)

        df = pd.read_csv(csv_path)

        # 选择预测器
        if args.predictor == "random_forest":
            predictor = RandomForestPredictor()
        else:
            raise ValueError(f"未知预测器: {args.predictor}")

        # 训练并保存预测 vs 真实
        y_pred, y_true, metrics = predictor.fit(df, args.target)
        preds_csv = f"{out_dir}/{args.predictor}_pred_vs_true.csv"
        pd.DataFrame({"y_pred": y_pred, "y_true": y_true}).to_csv(preds_csv, index=False)
        print(f"pred vs true -> {preds_csv}")

        # 保存模型（使用 args.predictor 字符串）
        model_out = f"{out_dir}/{args.predictor}.joblib"
        predictor.save(model_out)
        print(f"model saved -> {model_out}")
        print("metrics:", metrics)

    elif args.cmd == "predict":
        # 动态选择预测器
        if args.predictor == "random_forest":
            predictor = RandomForestPredictor.load(args.model_file)
        else:
            raise ValueError(f"未知预测器: {args.predictor}")

        kv = dict(s.split("=", 1) for s in args.params)
        params = {k: float(v) for k, v in kv.items()}
        yhat = predictor.predict(params)
        print({"prediction": yhat})

if __name__ == "__main__":
    main()

