{"cells": [{"cell_type": "code", "execution_count": 7, "id": "30e2c936", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import time\n", "import pandas as pd\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import r2_score"]}, {"cell_type": "code", "execution_count": 8, "id": "5fe1b416", "metadata": {}, "outputs": [], "source": ["class LinearBenchmark(object):\n", "    def __init__(self,\n", "                 batchsize,\n", "                 in_features,\n", "                 out_features,\n", "                 precision,\n", "                 activation_fct,\n", "                 use_bias,\n", "                 device,\n", "                 optimizer):\n", "        self.batchsize = batchsize\n", "        self.in_features = in_features\n", "        self.out_features = out_features\n", "        self.precision = precision\n", "        self.activation_fct = activation_fct\n", "        self.use_bias = use_bias\n", "        self.device = device\n", "        self.opt = optimizer\n", "\n", "    def create_model(self):\n", "        \"\"\"创建 Linear 模型\"\"\"\n", "        act = None\n", "        if self.activation_fct == \"relu\":\n", "            act = nn.ReLU()\n", "        elif self.activation_fct == \"tanh\":\n", "            act = nn.Tanh()\n", "        elif self.activation_fct is None:\n", "            act = None\n", "        else:\n", "            raise ValueError(f\"Unsupported activation {self.activation_fct}\")\n", "\n", "        layers = [nn.Linear(self.in_features, self.out_features, bias=self.use_bias)]\n", "        if act:\n", "            layers.append(act)\n", "\n", "        model = nn.Sequential(*layers).to(self.device)\n", "\n", "        # 设置精度\n", "        if self.precision == 16:\n", "            model = model.half()\n", "\n", "        return model\n", "\n", "    def benchmark_forward(self, iters=50, warmup=5):\n", "        \"\"\"前向传播耗时测试\"\"\"\n", "        model = self.create_model()\n", "        dtype = torch.float16 if self.precision == 16 else torch.float32\n", "        x = torch.ones(self.batchsize, self.in_features, dtype=dtype, device=self.device)\n", "\n", "        # 预热\n", "        for _ in range(warmup):\n", "            _ = model(x)\n", "\n", "        torch.cuda.synchronize() if self.device.startswith(\"cuda\") else None\n", "        t0 = time.time()\n", "        for _ in range(iters):\n", "            _ = model(x)\n", "        torch.cuda.synchronize() if self.device.startswith(\"cuda\") else None\n", "        t1 = time.time()\n", "\n", "        return (t1 - t0) / iters\n", "\n", "    def benchmark_train(self, iters=50, warmup=5):\n", "        \"\"\"前向+反向传播耗时测试\"\"\"\n", "        model = self.create_model()\n", "        dtype = torch.float16 if self.precision == 16 else torch.float32\n", "        x = torch.ones(self.batchsize, self.in_features, dtype=dtype, device=self.device)\n", "        target = torch.ones(self.batchsize, self.out_features, dtype=dtype, device=self.device)\n", "\n", "        criterion = nn.MS<PERSON><PERSON>()\n", "        if \"<PERSON>\" in self.opt:\n", "            optimizer = optim.<PERSON>(model.parameters(), lr=0.01)\n", "        elif \"SGD\" in self.opt:\n", "            optimizer = optim.SGD(model.parameters(), lr=0.01)\n", "        else:\n", "            raise ValueError(f\"Unsupported optimizer {self.opt}\")\n", "\n", "        # 预热\n", "        for _ in range(warmup):\n", "            optimizer.zero_grad()\n", "            y = model(x)\n", "            loss = criterion(y, target)\n", "            loss.backward()\n", "            optimizer.step()\n", "\n", "        torch.cuda.synchronize() if self.device.startswith(\"cuda\") else None\n", "        t0 = time.time()\n", "        for _ in range(iters):\n", "            optimizer.zero_grad()\n", "            y = model(x)\n", "            loss = criterion(y, target)\n", "            loss.backward()\n", "            optimizer.step()\n", "        torch.cuda.synchronize() if self.device.startswith(\"cuda\") else None\n", "        t1 = time.time()\n", "\n", "        return (t1 - t0) / iters"]}, {"cell_type": "code", "execution_count": 9, "id": "b1f9efd2", "metadata": {}, "outputs": [], "source": ["def benchmark_linear(batchsizes, in_features_list, out_features_list, device=\"cuda:0\"):\n", "    records = []\n", "    for b in batchsizes:\n", "        for fin in in_features_list:\n", "            for fout in out_features_list:\n", "                bench = LinearBenchmark(\n", "                    batchsize=b,\n", "                    in_features=fin,\n", "                    out_features=fout,\n", "                    precision=32,\n", "                    activation_fct=None,\n", "                    use_bias=True,\n", "                    device=device,\n", "                    optimizer=\"Adam\"\n", "                )\n", "                elapsed = bench.benchmark_forward()\n", "                records.append([b, fin, fout, elapsed])\n", "                print(f\"b={b}, in={fin}, out={fout}, time={elapsed:.6f}\")\n", "    df = pd.DataFrame(records, columns=[\"batch\", \"in_features\", \"out_features\", \"time\"])\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "id": "c360ab3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b=8, in=256, out=256, time=0.000021\n", "b=8, in=256, out=512, time=0.000019\n", "b=8, in=256, out=1024, time=0.000016\n", "b=8, in=256, out=2048, time=0.000015\n", "b=8, in=256, out=4096, time=0.000015\n", "b=8, in=256, out=8192, time=0.000015\n", "b=8, in=256, out=16384, time=0.000021\n", "b=8, in=512, out=256, time=0.000020\n", "b=8, in=512, out=512, time=0.000016\n", "b=8, in=512, out=1024, time=0.000016\n", "b=8, in=512, out=2048, time=0.000016\n", "b=8, in=512, out=4096, time=0.000016\n", "b=8, in=512, out=8192, time=0.000019\n", "b=8, in=512, out=16384, time=0.000035\n", "b=8, in=1024, out=256, time=0.000019\n", "b=8, in=1024, out=512, time=0.000015\n", "b=8, in=1024, out=1024, time=0.000015\n", "b=8, in=1024, out=2048, time=0.000016\n", "b=8, in=1024, out=4096, time=0.000019\n", "b=8, in=1024, out=8192, time=0.000035\n", "b=8, in=1024, out=16384, time=0.000063\n", "b=8, in=2048, out=256, time=0.000019\n", "b=8, in=2048, out=512, time=0.000016\n", "b=8, in=2048, out=1024, time=0.000016\n", "b=8, in=2048, out=2048, time=0.000021\n", "b=8, in=2048, out=4096, time=0.000039\n", "b=8, in=2048, out=8192, time=0.000069\n", "b=8, in=2048, out=16384, time=0.000106\n", "b=8, in=4096, out=256, time=0.000019\n", "b=8, in=4096, out=512, time=0.000022\n", "b=8, in=4096, out=1024, time=0.000025\n", "b=8, in=4096, out=2048, time=0.000039\n", "b=8, in=4096, out=4096, time=0.000087\n", "b=8, in=4096, out=8192, time=0.000106\n", "b=8, in=4096, out=16384, time=0.000198\n", "b=8, in=8192, out=256, time=0.000033\n", "b=8, in=8192, out=512, time=0.000049\n", "b=8, in=8192, out=1024, time=0.000058\n", "b=8, in=8192, out=2048, time=0.000091\n", "b=8, in=8192, out=4096, time=0.000109\n", "b=8, in=8192, out=8192, time=0.000261\n", "b=8, in=8192, out=16384, time=0.000308\n", "b=8, in=16384, out=256, time=0.000064\n", "b=8, in=16384, out=512, time=0.000083\n", "b=8, in=16384, out=1024, time=0.000112\n", "b=8, in=16384, out=2048, time=0.000128\n", "b=8, in=16384, out=4096, time=0.000183\n", "b=8, in=16384, out=8192, time=0.000320\n", "b=8, in=16384, out=16384, time=0.000596\n", "b=16, in=256, out=256, time=0.000020\n", "b=16, in=256, out=512, time=0.000016\n", "b=16, in=256, out=1024, time=0.000016\n", "b=16, in=256, out=2048, time=0.000016\n", "b=16, in=256, out=4096, time=0.000016\n", "b=16, in=256, out=8192, time=0.000019\n", "b=16, in=256, out=16384, time=0.000024\n", "b=16, in=512, out=256, time=0.000016\n", "b=16, in=512, out=512, time=0.000016\n", "b=16, in=512, out=1024, time=0.000016\n", "b=16, in=512, out=2048, time=0.000016\n", "b=16, in=512, out=4096, time=0.000019\n", "b=16, in=512, out=8192, time=0.000025\n", "b=16, in=512, out=16384, time=0.000043\n", "b=16, in=1024, out=256, time=0.000019\n", "b=16, in=1024, out=512, time=0.000016\n", "b=16, in=1024, out=1024, time=0.000016\n", "b=16, in=1024, out=2048, time=0.000019\n", "b=16, in=1024, out=4096, time=0.000033\n", "b=16, in=1024, out=8192, time=0.000062\n", "b=16, in=1024, out=16384, time=0.000085\n", "b=16, in=2048, out=256, time=0.000020\n", "b=16, in=2048, out=512, time=0.000015\n", "b=16, in=2048, out=1024, time=0.000021\n", "b=16, in=2048, out=2048, time=0.000034\n", "b=16, in=2048, out=4096, time=0.000065\n", "b=16, in=2048, out=8192, time=0.000119\n", "b=16, in=2048, out=16384, time=0.000163\n", "b=16, in=4096, out=256, time=0.000019\n", "b=16, in=4096, out=512, time=0.000025\n", "b=16, in=4096, out=1024, time=0.000039\n", "b=16, in=4096, out=2048, time=0.000071\n", "b=16, in=4096, out=4096, time=0.000135\n", "b=16, in=4096, out=8192, time=0.000231\n", "b=16, in=4096, out=16384, time=0.000320\n", "b=16, in=8192, out=256, time=0.000049\n", "b=16, in=8192, out=512, time=0.000055\n", "b=16, in=8192, out=1024, time=0.000075\n", "b=16, in=8192, out=2048, time=0.000112\n", "b=16, in=8192, out=4096, time=0.000195\n", "b=16, in=8192, out=8192, time=0.000299\n", "b=16, in=8192, out=16384, time=0.000570\n", "b=16, in=16384, out=256, time=0.000066\n", "b=16, in=16384, out=512, time=0.000087\n", "b=16, in=16384, out=1024, time=0.000120\n", "b=16, in=16384, out=2048, time=0.000198\n", "b=16, in=16384, out=4096, time=0.000375\n", "b=16, in=16384, out=8192, time=0.000580\n", "b=16, in=16384, out=16384, time=0.001117\n", "b=32, in=256, out=256, time=0.000020\n", "b=32, in=256, out=512, time=0.000019\n", "b=32, in=256, out=1024, time=0.000019\n", "b=32, in=256, out=2048, time=0.000020\n", "b=32, in=256, out=4096, time=0.000019\n", "b=32, in=256, out=8192, time=0.000019\n", "b=32, in=256, out=16384, time=0.000022\n", "b=32, in=512, out=256, time=0.000020\n", "b=32, in=512, out=512, time=0.000020\n", "b=32, in=512, out=1024, time=0.000019\n", "b=32, in=512, out=2048, time=0.000020\n", "b=32, in=512, out=4096, time=0.000019\n", "b=32, in=512, out=8192, time=0.000027\n", "b=32, in=512, out=16384, time=0.000040\n", "b=32, in=1024, out=256, time=0.000022\n", "b=32, in=1024, out=512, time=0.000019\n", "b=32, in=1024, out=1024, time=0.000027\n", "b=32, in=1024, out=2048, time=0.000034\n", "b=32, in=1024, out=4096, time=0.000028\n", "b=32, in=1024, out=8192, time=0.000051\n", "b=32, in=1024, out=16384, time=0.000116\n", "b=32, in=2048, out=256, time=0.000023\n", "b=32, in=2048, out=512, time=0.000024\n", "b=32, in=2048, out=1024, time=0.000038\n", "b=32, in=2048, out=2048, time=0.000050\n", "b=32, in=2048, out=4096, time=0.000057\n", "b=32, in=2048, out=8192, time=0.000095\n", "b=32, in=2048, out=16384, time=0.000147\n", "b=32, in=4096, out=256, time=0.000027\n", "b=32, in=4096, out=512, time=0.000029\n", "b=32, in=4096, out=1024, time=0.000053\n", "b=32, in=4096, out=2048, time=0.000078\n", "b=32, in=4096, out=4096, time=0.000136\n", "b=32, in=4096, out=8192, time=0.000185\n", "b=32, in=4096, out=16384, time=0.000289\n", "b=32, in=8192, out=256, time=0.000035\n", "b=32, in=8192, out=512, time=0.000039\n", "b=32, in=8192, out=1024, time=0.000088\n", "b=32, in=8192, out=2048, time=0.000144\n", "b=32, in=8192, out=4096, time=0.000297\n", "b=32, in=8192, out=8192, time=0.000365\n", "b=32, in=8192, out=16384, time=0.000569\n", "b=32, in=16384, out=256, time=0.000072\n", "b=32, in=16384, out=512, time=0.000113\n", "b=32, in=16384, out=1024, time=0.000173\n", "b=32, in=16384, out=2048, time=0.000259\n", "b=32, in=16384, out=4096, time=0.000431\n", "b=32, in=16384, out=8192, time=0.000722\n", "b=32, in=16384, out=16384, time=0.001135\n", "b=64, in=256, out=256, time=0.000019\n", "b=64, in=256, out=512, time=0.000020\n", "b=64, in=256, out=1024, time=0.000020\n", "b=64, in=256, out=2048, time=0.000020\n", "b=64, in=256, out=4096, time=0.000020\n", "b=64, in=256, out=8192, time=0.000020\n", "b=64, in=256, out=16384, time=0.000033\n", "b=64, in=512, out=256, time=0.000020\n", "b=64, in=512, out=512, time=0.000020\n", "b=64, in=512, out=1024, time=0.000025\n", "b=64, in=512, out=2048, time=0.000020\n", "b=64, in=512, out=4096, time=0.000022\n", "b=64, in=512, out=8192, time=0.000033\n", "b=64, in=512, out=16384, time=0.000061\n", "b=64, in=1024, out=256, time=0.000021\n", "b=64, in=1024, out=512, time=0.000019\n", "b=64, in=1024, out=1024, time=0.000033\n", "b=64, in=1024, out=2048, time=0.000028\n", "b=64, in=1024, out=4096, time=0.000040\n", "b=64, in=1024, out=8192, time=0.000063\n", "b=64, in=1024, out=16384, time=0.000121\n", "b=64, in=2048, out=256, time=0.000023\n", "b=64, in=2048, out=512, time=0.000037\n", "b=64, in=2048, out=1024, time=0.000040\n", "b=64, in=2048, out=2048, time=0.000054\n", "b=64, in=2048, out=4096, time=0.000076\n", "b=64, in=2048, out=8192, time=0.000120\n", "b=64, in=2048, out=16384, time=0.000235\n", "b=64, in=4096, out=256, time=0.000029\n", "b=64, in=4096, out=512, time=0.000053\n", "b=64, in=4096, out=1024, time=0.000067\n", "b=64, in=4096, out=2048, time=0.000085\n", "b=64, in=4096, out=4096, time=0.000165\n", "b=64, in=4096, out=8192, time=0.000233\n", "b=64, in=4096, out=16384, time=0.000462\n", "b=64, in=8192, out=256, time=0.000039\n", "b=64, in=8192, out=512, time=0.000084\n", "b=64, in=8192, out=1024, time=0.000096\n", "b=64, in=8192, out=2048, time=0.000163\n", "b=64, in=8192, out=4096, time=0.000322\n", "b=64, in=8192, out=8192, time=0.000462\n", "b=64, in=8192, out=16384, time=0.000917\n", "b=64, in=16384, out=256, time=0.000095\n", "b=64, in=16384, out=512, time=0.000158\n", "b=64, in=16384, out=1024, time=0.000157\n", "b=64, in=16384, out=2048, time=0.000265\n", "b=64, in=16384, out=4096, time=0.000468\n", "b=64, in=16384, out=8192, time=0.000916\n", "b=64, in=16384, out=16384, time=0.001828\n", "b=128, in=256, out=256, time=0.000019\n", "b=128, in=256, out=512, time=0.000020\n", "b=128, in=256, out=1024, time=0.000020\n", "b=128, in=256, out=2048, time=0.000020\n", "b=128, in=256, out=4096, time=0.000020\n", "b=128, in=256, out=8192, time=0.000033\n", "b=128, in=256, out=16384, time=0.000057\n", "b=128, in=512, out=256, time=0.000020\n", "b=128, in=512, out=512, time=0.000025\n", "b=128, in=512, out=1024, time=0.000020\n", "b=128, in=512, out=2048, time=0.000023\n", "b=128, in=512, out=4096, time=0.000033\n", "b=128, in=512, out=8192, time=0.000060\n", "b=128, in=512, out=16384, time=0.000110\n", "b=128, in=1024, out=256, time=0.000022\n", "b=128, in=1024, out=512, time=0.000034\n", "b=128, in=1024, out=1024, time=0.000028\n", "b=128, in=1024, out=2048, time=0.000041\n", "b=128, in=1024, out=4096, time=0.000062\n", "b=128, in=1024, out=8192, time=0.000116\n", "b=128, in=1024, out=16384, time=0.000174\n", "b=128, in=2048, out=256, time=0.000037\n", "b=128, in=2048, out=512, time=0.000050\n", "b=128, in=2048, out=1024, time=0.000052\n", "b=128, in=2048, out=2048, time=0.000077\n", "b=128, in=2048, out=4096, time=0.000119\n", "b=128, in=2048, out=8192, time=0.000183\n", "b=128, in=2048, out=16384, time=0.000337\n", "b=128, in=4096, out=256, time=0.000053\n", "b=128, in=4096, out=512, time=0.000077\n", "b=128, in=4096, out=1024, time=0.000086\n", "b=128, in=4096, out=2048, time=0.000137\n", "b=128, in=4096, out=4096, time=0.000239\n", "b=128, in=4096, out=8192, time=0.000400\n", "b=128, in=4096, out=16384, time=0.000780\n", "b=128, in=8192, out=256, time=0.000084\n", "b=128, in=8192, out=512, time=0.000095\n", "b=128, in=8192, out=1024, time=0.000162\n", "b=128, in=8192, out=2048, time=0.000238\n", "b=128, in=8192, out=4096, time=0.000398\n", "b=128, in=8192, out=8192, time=0.000750\n", "b=128, in=8192, out=16384, time=0.001552\n", "b=128, in=16384, out=256, time=0.000145\n", "b=128, in=16384, out=512, time=0.000157\n", "b=128, in=16384, out=1024, time=0.000265\n", "b=128, in=16384, out=2048, time=0.000405\n", "b=128, in=16384, out=4096, time=0.000714\n", "b=128, in=16384, out=8192, time=0.001391\n", "b=128, in=16384, out=16384, time=0.002593\n", "b=256, in=256, out=256, time=0.000019\n", "b=256, in=256, out=512, time=0.000020\n", "b=256, in=256, out=1024, time=0.000020\n", "b=256, in=256, out=2048, time=0.000020\n", "b=256, in=256, out=4096, time=0.000033\n", "b=256, in=256, out=8192, time=0.000057\n", "b=256, in=256, out=16384, time=0.000104\n", "b=256, in=512, out=256, time=0.000025\n", "b=256, in=512, out=512, time=0.000020\n", "b=256, in=512, out=1024, time=0.000023\n", "b=256, in=512, out=2048, time=0.000032\n", "b=256, in=512, out=4096, time=0.000061\n", "b=256, in=512, out=8192, time=0.000109\n", "b=256, in=512, out=16384, time=0.000201\n", "b=256, in=1024, out=256, time=0.000034\n", "b=256, in=1024, out=512, time=0.000028\n", "b=256, in=1024, out=1024, time=0.000041\n", "b=256, in=1024, out=2048, time=0.000059\n", "b=256, in=1024, out=4096, time=0.000103\n", "b=256, in=1024, out=8192, time=0.000177\n", "b=256, in=1024, out=16384, time=0.000391\n", "b=256, in=2048, out=256, time=0.000050\n", "b=256, in=2048, out=512, time=0.000052\n", "b=256, in=2048, out=1024, time=0.000079\n", "b=256, in=2048, out=2048, time=0.000119\n", "b=256, in=2048, out=4096, time=0.000182\n", "b=256, in=2048, out=8192, time=0.000344\n", "b=256, in=2048, out=16384, time=0.000772\n", "b=256, in=4096, out=256, time=0.000077\n", "b=256, in=4096, out=512, time=0.000085\n", "b=256, in=4096, out=1024, time=0.000138\n", "b=256, in=4096, out=2048, time=0.000229\n", "b=256, in=4096, out=4096, time=0.000402\n", "b=256, in=4096, out=8192, time=0.000779\n", "b=256, in=4096, out=16384, time=0.001534\n", "b=256, in=8192, out=256, time=0.000096\n", "b=256, in=8192, out=512, time=0.000160\n", "b=256, in=8192, out=1024, time=0.000249\n", "b=256, in=8192, out=2048, time=0.000397\n", "b=256, in=8192, out=4096, time=0.000750\n", "b=256, in=8192, out=8192, time=0.001435\n", "b=256, in=8192, out=16384, time=0.003059\n", "b=256, in=16384, out=256, time=0.000155\n", "b=256, in=16384, out=512, time=0.000264\n", "b=256, in=16384, out=1024, time=0.000411\n", "b=256, in=16384, out=2048, time=0.000725\n", "b=256, in=16384, out=4096, time=0.001401\n", "b=256, in=16384, out=8192, time=0.002750\n", "b=256, in=16384, out=16384, time=0.006112\n", "b=512, in=256, out=256, time=0.000019\n", "b=512, in=256, out=512, time=0.000020\n", "b=512, in=256, out=1024, time=0.000020\n", "b=512, in=256, out=2048, time=0.000033\n", "b=512, in=256, out=4096, time=0.000058\n", "b=512, in=256, out=8192, time=0.000104\n", "b=512, in=256, out=16384, time=0.000198\n", "b=512, in=512, out=256, time=0.000022\n", "b=512, in=512, out=512, time=0.000023\n", "b=512, in=512, out=1024, time=0.000032\n", "b=512, in=512, out=2048, time=0.000061\n", "b=512, in=512, out=4096, time=0.000109\n", "b=512, in=512, out=8192, time=0.000200\n", "b=512, in=512, out=16384, time=0.000381\n", "b=512, in=1024, out=256, time=0.000028\n", "b=512, in=1024, out=512, time=0.000042\n", "b=512, in=1024, out=1024, time=0.000060\n", "b=512, in=1024, out=2048, time=0.000095\n", "b=512, in=1024, out=4096, time=0.000198\n", "b=512, in=1024, out=8192, time=0.000391\n", "b=512, in=1024, out=16384, time=0.000746\n", "b=512, in=2048, out=256, time=0.000053\n", "b=512, in=2048, out=512, time=0.000079\n", "b=512, in=2048, out=1024, time=0.000114\n", "b=512, in=2048, out=2048, time=0.000182\n", "b=512, in=2048, out=4096, time=0.000393\n", "b=512, in=2048, out=8192, time=0.000772\n", "b=512, in=2048, out=16384, time=0.001478\n", "b=512, in=4096, out=256, time=0.000086\n", "b=512, in=4096, out=512, time=0.000137\n", "b=512, in=4096, out=1024, time=0.000229\n", "b=512, in=4096, out=2048, time=0.000404\n", "b=512, in=4096, out=4096, time=0.000778\n", "b=512, in=4096, out=8192, time=0.001534\n", "b=512, in=4096, out=16384, time=0.002940\n", "b=512, in=8192, out=256, time=0.000161\n", "b=512, in=8192, out=512, time=0.000249\n", "b=512, in=8192, out=1024, time=0.000398\n", "b=512, in=8192, out=2048, time=0.000747\n", "b=512, in=8192, out=4096, time=0.001437\n", "b=512, in=8192, out=8192, time=0.003059\n", "b=512, in=8192, out=16384, time=0.005873\n", "b=512, in=16384, out=256, time=0.000252\n", "b=512, in=16384, out=512, time=0.000414\n", "b=512, in=16384, out=1024, time=0.000730\n", "b=512, in=16384, out=2048, time=0.001403\n", "b=512, in=16384, out=4096, time=0.002757\n", "b=512, in=16384, out=8192, time=0.006124\n", "b=512, in=16384, out=16384, time=0.011739\n", "R²: 0.7174306845403298\n", "配置={'batch': 32, 'in_features': 2048, 'out_features': 256}, 实际耗时=0.000023, 预测耗时=0.000024\n", "配置={'batch': 8, 'in_features': 16384, 'out_features': 256}, 实际耗时=0.000064, 预测耗时=0.000073\n", "配置={'batch': 64, 'in_features': 16384, 'out_features': 8192}, 实际耗时=0.000916, 预测耗时=0.000996\n", "配置={'batch': 256, 'in_features': 16384, 'out_features': 512}, 实际耗时=0.000264, 预测耗时=0.000293\n", "配置={'batch': 16, 'in_features': 8192, 'out_features': 256}, 实际耗时=0.000049, 预测耗时=0.000040\n", "配置={'batch': 512, 'in_features': 16384, 'out_features': 4096}, 实际耗时=0.002757, 预测耗时=0.001515\n", "配置={'batch': 512, 'in_features': 256, 'out_features': 512}, 实际耗时=0.000020, 预测耗时=0.000022\n", "配置={'batch': 16, 'in_features': 8192, 'out_features': 16384}, 实际耗时=0.000570, 预测耗时=0.000397\n", "配置={'batch': 8, 'in_features': 2048, 'out_features': 4096}, 实际耗时=0.000039, 预测耗时=0.000053\n", "配置={'batch': 16, 'in_features': 2048, 'out_features': 2048}, 实际耗时=0.000034, 预测耗时=0.000038\n", "配置={'batch': 16, 'in_features': 2048, 'out_features': 16384}, 实际耗时=0.000163, 预测耗时=0.000113\n", "配置={'batch': 64, 'in_features': 512, 'out_features': 2048}, 实际耗时=0.000020, 预测耗时=0.000023\n", "配置={'batch': 32, 'in_features': 4096, 'out_features': 256}, 实际耗时=0.000027, 预测耗时=0.000028\n", "配置={'batch': 128, 'in_features': 512, 'out_features': 16384}, 实际耗时=0.000110, 预测耗时=0.000071\n", "配置={'batch': 32, 'in_features': 512, 'out_features': 2048}, 实际耗时=0.000020, 预测耗时=0.000020\n", "配置={'batch': 512, 'in_features': 8192, 'out_features': 2048}, 实际耗时=0.000747, 预测耗时=0.000561\n", "配置={'batch': 128, 'in_features': 2048, 'out_features': 16384}, 实际耗时=0.000337, 预测耗时=0.000259\n", "配置={'batch': 32, 'in_features': 8192, 'out_features': 16384}, 实际耗时=0.000569, 预测耗时=0.000420\n", "配置={'batch': 64, 'in_features': 256, 'out_features': 8192}, 实际耗时=0.000020, 预测耗时=0.000029\n", "配置={'batch': 256, 'in_features': 4096, 'out_features': 1024}, 实际耗时=0.000138, 预测耗时=0.000166\n"]}], "source": ["import numpy as np\n", "\n", "# ======================== 运行基准测试 ========================\n", "batchsizes = [8, 16, 32, 64, 128, 256, 512]\n", "in_features_list = [256, 512, 1024, 2048, 4096, 8192, 16384]\n", "out_features_list = [256, 512, 1024, 2048, 4096, 8192, 16384]\n", "\n", "df = benchmark_linear(batchsizes, in_features_list, out_features_list)\n", "\n", "# 同时需要优化特征，计算特征(flops)，内存特征(Roofline)，以及硬件特征\n", "X = df[[\"batch\", \"in_features\", \"out_features\"]]\n", "y = df[\"time\"]\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "rf = RandomForestRegressor(n_estimators=100, random_state=42)\n", "rf.fit(X_train, y_train)\n", "\n", "pred = rf.predict(X_test)\n", "print(\"R²:\", r2_score(y_test, pred))\n", "\n", "# ======================== 随机取几组对比 ========================\n", "idx = np.random.choice(len(X_test), size=20, replace=False)  # 随机挑 5 个样本\n", "for i in idx:\n", "    config = X_test.iloc[i].to_dict()\n", "    actual = y_test.iloc[i]\n", "    predicted = pred[i]\n", "    print(f\"配置={config}, 实际耗时={actual:.6f}, 预测耗时={predicted:.6f}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}