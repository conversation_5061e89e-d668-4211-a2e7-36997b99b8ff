{"cells": [{"cell_type": "code", "execution_count": 19, "id": "45414383", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.profiler import profile, record_function, ProfilerActivity\n", "from transformers import ViTForImageClassification\n", "import torch.nn as nn"]}, {"cell_type": "code", "execution_count": 20, "id": "0b1c5ec9", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of ViTForImageClassification were not initialized from the model checkpoint at google/vit-base-patch16-224 and are newly initialized because the shapes did not match:\n", "- classifier.bias: found shape torch.<PERSON><PERSON>([1000]) in the checkpoint and torch.<PERSON><PERSON>([10]) in the model instantiated\n", "- classifier.weight: found shape torch.<PERSON><PERSON>([1000, 768]) in the checkpoint and torch.<PERSON><PERSON>([10, 768]) in the model instantiated\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["ViTForImageClassification\n", "ViTForImageClassification(\n", "  (vit): ViTModel(\n", "    (embeddings): ViTEmbeddings(\n", "      (patch_embeddings): ViTPatchEmbeddings(\n", "        (projection): Conv2d(3, 768, kernel_size=(16, 16), stride=(16, 16))\n", "      )\n", "      (dropout): Dropout(p=0.0, inplace=False)\n", "    )\n", "    (encoder): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "      (layer): ModuleList(\n", "        (0-11): 12 x ViTLayer(\n", "          (attention): V<PERSON><PERSON><PERSON><PERSON>(\n", "            (attention): <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "              (query): Linear(in_features=768, out_features=768, bias=True)\n", "              (key): Linear(in_features=768, out_features=768, bias=True)\n", "              (value): Linear(in_features=768, out_features=768, bias=True)\n", "            )\n", "            (output): ViTSelfOutput(\n", "              (dense): Linear(in_features=768, out_features=768, bias=True)\n", "              (dropout): Dropout(p=0.0, inplace=False)\n", "            )\n", "          )\n", "          (intermediate): ViTIntermediate(\n", "            (dense): Linear(in_features=768, out_features=3072, bias=True)\n", "            (intermediate_act_fn): GELUActivation()\n", "          )\n", "          (output): ViTOutput(\n", "            (dense): Linear(in_features=3072, out_features=768, bias=True)\n", "            (dropout): Dropout(p=0.0, inplace=False)\n", "          )\n", "          (layernorm_before): LayerNorm((768,), eps=1e-12, elementwise_affine=True)\n", "          (layernorm_after): LayerNorm((768,), eps=1e-12, elementwise_affine=True)\n", "        )\n", "      )\n", "    )\n", "    (layernorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)\n", "  )\n", "  (classifier): Linear(in_features=768, out_features=10, bias=True)\n", ")\n"]}], "source": ["device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "\n", "num_labels = 10\n", "model = ViTForImageClassification.from_pretrained(\n", "    \"google/vit-base-patch16-224\",\n", "    num_labels=num_labels,\n", "    ignore_mismatched_sizes=True\n", ").to(device)\n", "\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=1e-4)\n", "print(model.__class__.__name__)\n", "print(model)"]}, {"cell_type": "code", "execution_count": 21, "id": "b790a369", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["整体 ViT 推理时间: 4.951 ms\n"]}], "source": ["def measure_time_whole(model, inp, repeat=10):\n", "    torch.cuda.synchronize()\n", "    start = torch.cuda.Event(enable_timing=True)\n", "    end = torch.cuda.Event(enable_timing=True)\n", "\n", "    times = []\n", "    with torch.no_grad():\n", "        for _ in range(repeat):\n", "            start.record()\n", "            _ = model(inp)\n", "            end.record()\n", "            torch.cuda.synchronize()\n", "            times.append(start.elapsed_time(end))\n", "    return sum(times) / len(times)\n", "# 假设输入一张 224x224 的 RGB 图像\n", "inp = torch.randn(1, 3, 224, 224, device=device)\n", "time_whole = measure_time_whole(model, inp)\n", "print(f\"整体 ViT 推理时间: {time_whole:.3f} ms\")"]}, {"cell_type": "code", "execution_count": null, "id": "2821788c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'layer_0': 0.4619840085506439, 'layer_1': 0.4240959882736206, 'layer_2': 0.43702399730682373, 'layer_3': 0.4244160056114197, 'layer_4': 0.4381439983844757, 'layer_5': 0.4177280068397522, 'layer_6': 0.43887999653816223, 'layer_7': 0.4163840115070343, 'layer_8': 0.46560001373291016, 'layer_9': 0.4190720021724701, 'layer_10': 0.4461440145969391, 'layer_11': 0.41865599155426025}\n"]}], "source": ["def measure_time_per_layer_and_sublayer(model, inp, repeat=10):\n", "    \"\"\"\n", "    按 ViT 的真实结构测量子层时间:\n", "    - layernorm_before\n", "    - attention\n", "    - layernorm_after\n", "    - intermediate (FFN 第一层)\n", "    - output (FFN 第二层)\n", "    \"\"\"\n", "    backbone = model.vit  # ViTForImageClassification → ViTModel\n", "    torch.cuda.synchronize()\n", "    out = backbone.embeddings(inp)   # patch embedding\n", "    times = {}\n", "\n", "    with torch.no_grad():\n", "        for i, blk in enumerate(backbone.encoder.layer):\n", "            # --- Layer<PERSON><PERSON> before ---\n", "            torch.cuda.synchronize()\n", "            start = torch.cuda.Event(enable_timing=True)\n", "            end = torch.cuda.Event(enable_timing=True)\n", "            for _ in range(repeat):\n", "                start.record()\n", "                tmp = blk.layernorm_before(out)\n", "                end.record()\n", "                torch.cuda.synchronize()\n", "            times[f\"layer{i}_ln_before\"] = start.elapsed_time(end)\n", "\n", "            # --- Attention ---\n", "            torch.cuda.synchronize()\n", "            start.record()\n", "            attn_out = blk.attention(tmp)[0]  # 只取 hidden_state\n", "            end.record()\n", "            torch.cuda.synchronize()\n", "            times[f\"layer{i}_attn\"] = start.elapsed_time(end)\n", "\n", "            out = out + attn_out  # 残差\n", "\n", "            # --- Layer<PERSON>orm after ---\n", "            torch.cuda.synchronize()\n", "            start.record()\n", "            tmp = blk.layernorm_after(out)\n", "            end.record()\n", "            torch.cuda.synchronize()\n", "            times[f\"layer{i}_ln_after\"] = start.elapsed_time(end)\n", "\n", "            # --- Intermediate (FFN 第1层 Linear+GELU) ---\n", "            torch.cuda.synchronize()\n", "            start.record()\n", "            inter_out = blk.intermediate(tmp)\n", "            end.record()\n", "            torch.cuda.synchronize()\n", "            times[f\"layer{i}_ffn_intermediate\"] = start.elapsed_time(end)\n", "\n", "            # --- Output (FFN 第2层 Linear+Dropout) ---\n", "            torch.cuda.synchronize()\n", "            start.record()\n", "            ffn_out = blk.output(inter_out)\n", "            end.record()\n", "            torch.cuda.synchronize()\n", "            times[f\"layer{i}_ffn_output\"] = start.elapsed_time(end)\n", "\n", "            out = out + ffn_out  # 残差\n", "\n", "    return times\n", "\n", "inp = torch.randn(1, 3, 224, 224, device=device)\n", "layer_times = measure_time_per_layer_and_sublayer(model, inp)\n", "print(layer_times)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}