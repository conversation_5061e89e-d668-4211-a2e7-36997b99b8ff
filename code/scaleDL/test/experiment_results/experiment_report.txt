Layer-Centric Performance Prediction - Experiment Report
============================================================

1. LAYER CHARACTERISTICS ANALYSIS
----------------------------------------
Total layers analyzed: 200
Layer types: ['LayerNorm', 'Dropout', 'Conv2d', 'Linear', 'MultiheadAttention']
Average parallelizability: 0.739
Average memory intensity: 0.594

2. ARCHITECTURE COMPARISON
----------------------------------------
SimpleModel:
  - Parameters: 2,827,620
  - FLOPs: 1.50e+09
  - Parallelizability: 0.750
  - Memory Intensity: 0.400

BERT-Base:
  - Parameters: 110,000,000
  - FLOPs: 2.20e+10
  - Parallelizability: 0.680
  - Memory Intensity: 0.600

ViT-Base:
  - Parameters: 86,000,000
  - FLOPs: 1.80e+10
  - Parallelizability: 0.720
  - Memory Intensity: 0.500

GPT-2:
  - Parameters: 124,000,000
  - FLOPs: 3.60e+10
  - Parallelizability: 0.650
  - Memory Intensity: 0.700

3. DISTRIBUTED SCALING INSIGHTS
----------------------------------------
Maximum parallel efficiency: 1.000
Optimal configuration: 1.0 GPUs, 25.0 GB/s

4. FEATURE IMPORTANCE SUMMARY
----------------------------------------
Training Time: Most important feature is 'total_flops' (0.239)
Inference Time: Most important feature is 'total_flops' (0.339)
Memory Usage: Most important feature is 'total_params' (0.360)
Distributed Efficiency: Most important feature is 'avg_parallelizability' (0.293)

5. PREDICTION ACCURACY SUMMARY
----------------------------------------
Average prediction accuracy (R²): 0.828

6. KEY FINDINGS
----------------------------------------
- Layer-centric modeling provides fine-grained performance insights
- Different layer types exhibit distinct performance characteristics
- Parallelizability is a key factor for distributed scaling
- Communication overhead significantly impacts large-scale performance
- Feature importance varies significantly across different prediction tasks
