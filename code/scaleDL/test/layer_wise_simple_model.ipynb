{"cells": [{"cell_type": "code", "execution_count": 2, "id": "45414383", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.profiler import profile, record_function, ProfilerActivity"]}, {"cell_type": "code", "execution_count": 3, "id": "0b1c5ec9", "metadata": {}, "outputs": [], "source": ["# ====== 1. 定义模型 ======\n", "class SimpleModel(nn.Module):\n", "    def __init__(self, input_dim=100, hidden_dim=64, num_classes=10, nhead=4):\n", "        super().__init__()\n", "        self.embedding = nn.Linear(input_dim, hidden_dim)\n", "        encoder_layer = nn.TransformerEncoderLayer(d_model=hidden_dim, nhead=nhead, dim_feedforward=128)\n", "        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=2)\n", "        self.dropout_layer = nn.Dropout(0.1)\n", "        self.classifier = nn.Linear(hidden_dim, num_classes)\n", "\n", "    def forward(self, x):                 # x: (S, N, E_in)\n", "        x = self.embedding(x)             # (S, N, H)\n", "        x = self.transformer_encoder(x)   # (S, N, H)\n", "        x = self.dropout_layer(x)         # (S, N, H)\n", "        x = x.mean(dim=0)                 # (N, H) 也可用 x[-1] 或 x[0]\n", "        x = self.classifier(x)            # (N, C)\n", "        return x"]}, {"cell_type": "code", "execution_count": 4, "id": "2821788c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SimpleModel(\n", "  (embedding): Linear(in_features=100, out_features=64, bias=True)\n", "  (transformer_encoder): TransformerEncoder(\n", "    (layers): ModuleList(\n", "      (0-1): 2 x TransformerEncoderLayer(\n", "        (self_attn): MultiheadAttention(\n", "          (out_proj): NonDynamicallyQuantizableLinear(in_features=64, out_features=64, bias=True)\n", "        )\n", "        (linear1): Linear(in_features=64, out_features=128, bias=True)\n", "        (dropout): Dropout(p=0.1, inplace=False)\n", "        (linear2): Linear(in_features=128, out_features=64, bias=True)\n", "        (norm1): LayerNorm((64,), eps=1e-05, elementwise_affine=True)\n", "        (norm2): LayerNorm((64,), eps=1e-05, elementwise_affine=True)\n", "        (dropout1): Dropout(p=0.1, inplace=False)\n", "        (dropout2): Dropout(p=0.1, inplace=False)\n", "      )\n", "    )\n", "  )\n", "  (dropout_layer): Dropout(p=0.1, inplace=False)\n", "  (classifier): Linear(in_features=64, out_features=10, bias=True)\n", ")\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/scaleDL/lib/python3.12/site-packages/torch/nn/modules/transformer.py:379: UserWarning: enable_nested_tensor is True, but self.use_nested_tensor is False because encoder_layer.self_attn.batch_first was not True(use batch_first for better inference performance)\n", "  warnings.warn(\n"]}], "source": ["model = SimpleModel()\n", "print(model)"]}, {"cell_type": "code", "execution_count": 5, "id": "ddecb98d", "metadata": {}, "outputs": [], "source": ["# ====== 2. 创建输入 ======\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "model = SimpleModel().to(device)\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=1e-3)\n", "\n", "batch_size, seq_len, input_dim = 32, 20, 100\n", "x = torch.randn(seq_len, batch_size, input_dim, device=device)  # Transformer 输入格式: (S, N, E)\n", "y = torch.randint(0, 10, (batch_size,), device=device)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "57ea014a", "metadata": {}, "outputs": [], "source": ["# ====== 3. 整模型训练时间 ======\n", "def measure_model_train_time(model, x, y, warmup=5, iters=10):\n", "    for _ in range(warmup):\n", "        optimizer.zero_grad()\n", "        loss = criterion(model(x), y)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "    torch.cuda.synchronize()\n", "    start_event = torch.cuda.Event(enable_timing=True)\n", "    end_event = torch.cuda.Event(enable_timing=True)\n", "\n", "    start_event.record()\n", "    for _ in range(iters):\n", "        optimizer.zero_grad()\n", "        loss = criterion(model(x), y)\n", "        loss.backward()\n", "        optimizer.step()\n", "    end_event.record()\n", "    torch.cuda.synchronize()\n", "\n", "    return start_event.elapsed_time(end_event) / iters  # 平均每次迭代 (ms)"]}, {"cell_type": "code", "execution_count": 7, "id": "6c86af70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[整模型] 平均训练时间: 3.011 ms\n"]}], "source": ["model_time = measure_model_train_time(model, x, y)\n", "print(f\"[整模型] 平均训练时间: {model_time:.3f} ms\")"]}, {"cell_type": "code", "execution_count": 8, "id": "3c13421a", "metadata": {}, "outputs": [], "source": ["# ====== 4. 单层时间测量 ======\n", "def measure_layer_time(layer, x, grad_shape, iters=10, is_attn=False):\n", "    optimizer = optim.Adam(layer.parameters(), lr=1e-3)\n", "    grad_out = torch.randn(grad_shape, device=x.device)\n", "\n", "    torch.cuda.synchronize()\n", "    start_event = torch.cuda.Event(enable_timing=True)\n", "    end_event = torch.cuda.Event(enable_timing=True)\n", "\n", "    start_event.record()\n", "    for _ in range(iters):\n", "        optimizer.zero_grad()\n", "        if is_attn:  # MultiheadAttention\n", "            out, _ = layer(x, x, x)\n", "        else:\n", "            out = layer(x)\n", "        out.backward(grad_out)\n", "        optimizer.step()\n", "    end_event.record()\n", "    torch.cuda.synchronize()\n", "\n", "    return start_event.elapsed_time(end_event) / iters\n"]}, {"cell_type": "code", "execution_count": 9, "id": "b16ed9f2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "[单层测量结果]\n", "embedding : 0.395 ms\n", "attn      : 1.114 ms\n", "linear1   : 0.343 ms\n", "linear2   : 0.331 ms\n", "norm1     : 0.270 ms\n", "norm2     : 0.273 ms\n", "classifier: 0.322 ms\n", "\n", "[累加时间] 3.048 ms vs [整模型] 3.011 ms\n"]}], "source": ["# ---- 单层拆解测量 ----\n", "layer_times = {}\n", "\n", "# embedding\n", "inp = torch.randn(seq_len, batch_size, input_dim, device=device)\n", "layer_times[\"embedding\"] = measure_layer_time(model.embedding, inp, (seq_len, batch_size, 64))\n", "\n", "# encoder layer 0\n", "encoder0 = model.transformer_encoder.layers[0]\n", "inp = torch.randn(seq_len, batch_size, 64, device=device)\n", "\n", "layer_times[\"attn\"] = measure_layer_time(encoder0.self_attn, inp, (seq_len, batch_size, 64), is_attn=True)\n", "layer_times[\"linear1\"] = measure_layer_time(encoder0.linear1, inp, (seq_len, batch_size, 128))\n", "layer_times[\"linear2\"] = measure_layer_time(\n", "    encoder0.linear2,\n", "    torch.randn(seq_len, batch_size, 128, device=device),\n", "    (seq_len, batch_size, 64)\n", ")\n", "layer_times[\"norm1\"] = measure_layer_time(encoder0.norm1, inp, (seq_len, batch_size, 64))\n", "layer_times[\"norm2\"] = measure_layer_time(encoder0.norm2, inp, (seq_len, batch_size, 64))\n", "# classifier\n", "inp = torch.randn(batch_size, 64, device=device)\n", "layer_times[\"classifier\"] = measure_layer_time(model.classifier, inp, (batch_size, 10))\n", "\n", "# ---- 输出结果 ----\n", "print(\"\\n[单层测量结果]\")\n", "for k, v in layer_times.items():\n", "    print(f\"{k:10s}: {v:.3f} ms\")\n", "\n", "print(f\"\\n[累加时间] {sum(layer_times.values()):.3f} ms vs [整模型] {model_time:.3f} ms\")"]}, {"cell_type": "code", "execution_count": 10, "id": "0185e5f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["整体 TransformerEncoderLayer 时间: 1.618 ms\n", "\n", "[拆解子层时间]\n", "attn      : 1.076 ms\n", "norm1     : 0.282 ms\n", "linear1   : 0.340 ms\n", "linear2   : 0.335 ms\n", "norm2     : 0.270 ms\n", "\n", "拆解子层累加: 2.304 ms\n", "整体层时间   : 1.618 ms\n"]}], "source": ["# ====== 测试整体 vs 拆解 ======\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "seq_len, batch_size, hidden_dim = 20, 32, 64\n", "\n", "# 定义一层 encoder\n", "encoder_layer = nn.TransformerEncoderLayer(\n", "    d_model=hidden_dim, nhead=4, dim_feedforward=128\n", ").to(device)\n", "\n", "inp = torch.randn(seq_len, batch_size, hidden_dim, device=device)\n", "\n", "# ---- (1) 整体 TransformerEncoderLayer ----\n", "time_whole = measure_layer_time(\n", "    encoder_layer, inp, (seq_len, batch_size, hidden_dim)\n", ")\n", "print(f\"整体 TransformerEncoderLayer 时间: {time_whole:.3f} ms\")\n", "\n", "# ---- (2) 拆解 TransformerEncoderLayer ----\n", "layer_times = {}\n", "\n", "# attention\n", "layer_times[\"attn\"] = measure_layer_time(\n", "    encoder_layer.self_attn, inp, (seq_len, batch_size, hidden_dim), is_attn=True\n", ")\n", "\n", "# norm1\n", "layer_times[\"norm1\"] = measure_layer_time(\n", "    encoder_layer.norm1, inp, (seq_len, batch_size, hidden_dim)\n", ")\n", "\n", "# linear1\n", "layer_times[\"linear1\"] = measure_layer_time(\n", "    encoder_layer.linear1, inp, (seq_len, batch_size, 128)\n", ")\n", "\n", "# linear2\n", "inp_linear2 = torch.randn(seq_len, batch_size, 128, device=device)\n", "layer_times[\"linear2\"] = measure_layer_time(\n", "    encoder_layer.linear2, inp_linear2, (seq_len, batch_size, hidden_dim)\n", ")\n", "\n", "# norm2\n", "layer_times[\"norm2\"] = measure_layer_time(\n", "    encoder_layer.norm2, inp, (seq_len, batch_size, hidden_dim)\n", ")\n", "\n", "print(\"\\n[拆解子层时间]\")\n", "for k, v in layer_times.items():\n", "    print(f\"{k:10s}: {v:.3f} ms\")\n", "\n", "print(f\"\\n拆解子层累加: {sum(layer_times.values()):.3f} ms\")\n", "print(f\"整体层时间   : {time_whole:.3f} ms\")"]}, {"cell_type": "code", "execution_count": 22, "id": "4d4614eb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "整体执行 CUDA kernels:\n", "-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "                                                   Name    Self CPU %      Self CPU   CPU total %     CPU total  CPU time avg     Self CUDA   Self CUDA %    CUDA total  CUDA time avg    # of Calls  \n", "-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "    autograd::engine::evaluate_function: AddmmBackward0         0.86%      36.683us        10.00%     425.055us     106.264us       0.000us         0.00%      79.904us      19.976us             4  \n", "                                              aten::sum         2.43%     103.264us         3.32%     141.184us      23.531us      47.743us        21.13%      47.743us       7.957us             6  \n", "                                         AddmmBackward0         0.56%      23.973us         7.04%     299.471us      74.868us       0.000us         0.00%      41.857us      10.464us             4  \n", "                                               aten::mm         3.94%     167.571us         5.78%     245.903us      35.129us      41.857us        18.52%      41.857us       5.980us             7  \n", "void at::native::reduce_kernel<128, 4, at::native::R...         0.00%       0.000us         0.00%       0.000us       0.000us      38.047us        16.84%      38.047us       9.512us             4  \n", "autograd::engine::evaluate_function: ScaledDotProduc...         0.27%      11.552us         4.63%     196.901us     196.901us       0.000us         0.00%      31.520us      31.520us             1  \n", "            ScaledDotProductEfficientAttentionBackward0         0.13%       5.711us         4.36%     185.349us     185.349us       0.000us         0.00%      31.520us      31.520us             1  \n", "aten::_scaled_dot_product_efficient_attention_backwa...         0.60%      25.397us         4.23%     179.638us     179.638us       0.000us         0.00%      31.520us      31.520us             1  \n", "                    aten::_efficient_attention_backward         0.89%      37.928us         3.45%     146.831us     146.831us      22.464us         9.94%      31.520us      31.520us             1  \n", "                                            aten::copy_         2.56%     109.035us         5.65%     240.355us      16.024us      31.393us        13.89%      31.393us       2.093us            15  \n", "                                            aten::clone         0.66%      28.054us         6.94%     294.908us      24.576us       0.000us         0.00%      27.553us       2.296us            12  \n", "void at::native::elementwise_kernel<128, 2, at::nati...         0.00%       0.000us         0.00%       0.000us       0.000us      27.553us        12.19%      27.553us       2.296us            12  \n", "fmha_cutlassB_f32_aligned_64x64_k32_dropout_sm80(PyT...         0.00%       0.000us         0.00%       0.000us       0.000us      22.464us         9.94%      22.464us      22.464us             1  \n", "void cutlass::<PERSON>el<cutlass_80_simt_sgemm_64x64_8x5...         0.00%       0.000us         0.00%       0.000us       0.000us      19.457us         8.61%      19.457us       4.864us             4  \n", "                                           aten::linear         0.90%      38.398us        48.84%       2.076ms     519.057us       0.000us         0.00%      18.432us       4.608us             4  \n", "-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "Self CPU time total: 4.251ms\n", "Self CUDA time total: 225.952us\n", "\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "from torch.profiler import profile, ProfilerActivity\n", "\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "seq_len, batch_size, hidden_dim = 20, 32, 64\n", "\n", "# 定义一层 encoder\n", "encoder_layer = nn.TransformerEncoderLayer(\n", "    d_model=hidden_dim, nhead=4, dim_feedforward=128, dropout=0.1, activation=\"relu\"\n", ").to(device)\n", "\n", "inp = torch.randn(seq_len, batch_size, hidden_dim, device=device)\n", "\n", "# -------- (1) 整体模式 --------\n", "with profile(activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA]) as prof:\n", "    out = encoder_layer(inp)\n", "    loss = out.sum()\n", "    loss.backward()\n", "print(\"\\n整体执行 CUDA kernels:\")\n", "print(prof.key_averages().table(sort_by=\"cuda_time_total\", row_limit=15))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "ab6cc7fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "拆解执行 CUDA kernels:\n", "-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "                                                   Name    Self CPU %      Self CPU   CPU total %     CPU total  CPU time avg     Self CUDA   Self CUDA %    CUDA total  CUDA time avg    # of Calls  \n", "-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "    autograd::engine::evaluate_function: AddmmBackward0         0.99%      37.761us        10.66%     408.705us     102.176us       0.000us         0.00%      78.975us      19.744us             4  \n", "                                              aten::sum         1.78%      68.050us         2.62%     100.324us      20.065us      43.904us        19.52%      43.904us       8.781us             5  \n", "                                         AddmmBackward0         0.63%      24.190us         7.73%     296.226us      74.056us       0.000us         0.00%      42.015us      10.504us             4  \n", "                                               aten::mm         4.19%     160.683us         6.30%     241.484us      34.498us      42.015us        18.68%      42.015us       6.002us             7  \n", "void at::native::reduce_kernel<128, 4, at::native::R...         0.00%       0.000us         0.00%       0.000us       0.000us      36.960us        16.43%      36.960us       9.240us             4  \n", "sm80_xmma_gemm_f32f32_f32f32_f32_tn_n_tilesize32x32x...         0.00%       0.000us         0.00%       0.000us       0.000us      24.991us        11.11%      24.991us       4.165us             6  \n", "void at::native::vectorized_elementwise_kernel<4, at...         0.00%       0.000us         0.00%       0.000us       0.000us      22.691us        10.09%      22.691us       1.335us            17  \n", "                                              aten::bmm         4.02%     154.075us         5.46%     209.298us      34.883us      21.856us         9.72%      21.856us       3.643us             6  \n", "sm80_xmma_gemm_f32f32_f32f32_f32_nn_n_tilesize32x32x...         0.00%       0.000us         0.00%       0.000us       0.000us      21.024us         9.35%      21.024us       4.205us             5  \n", "                                             aten::add_         1.86%      71.427us         3.70%     141.691us       9.446us      19.811us         8.81%      19.811us       1.321us            15  \n", "void cutlass::Kernel<cutlass_80_simt_sgemm_64x64_8x5...         0.00%       0.000us         0.00%       0.000us       0.000us      19.648us         8.74%      19.648us       4.912us             4  \n", "                                            aten::copy_         3.19%     122.186us         6.16%     236.068us      26.230us      18.655us         8.29%      18.655us       2.073us             9  \n", "                                           aten::linear         1.05%      40.143us        36.81%       1.411ms     352.686us       0.000us         0.00%      18.047us       4.512us             4  \n", "                                            aten::addmm        22.56%     864.509us        33.06%       1.267ms     316.769us      18.047us         8.02%      18.047us       4.512us             4  \n", "autograd::engine::evaluate_function: NativeLayerNorm...         0.74%      28.302us         5.87%     225.146us     112.573us       0.000us         0.00%      17.984us       8.992us             2  \n", "-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "Self CPU time total: 3.833ms\n", "Self CUDA time total: 224.897us\n", "\n"]}], "source": ["# -------- (2) 拆解模式 (等价 forward) --------\n", "with profile(activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA]) as prof:\n", "    # ---- Self-Attention 子层 ----\n", "    attn_out, _ = encoder_layer.self_attn(inp, inp, inp)\n", "    out = inp + encoder_layer.dropout1(attn_out)\n", "    out = encoder_layer.norm1(out)\n", "\n", "    # ---- Feed Forward 子层 ----\n", "    ff_out = encoder_layer.linear2(\n", "        encoder_layer.dropout(\n", "            encoder_layer.activation(encoder_layer.linear1(out))\n", "        )\n", "    )\n", "    out = out + encoder_layer.dropout2(ff_out)\n", "    out = encoder_layer.norm2(out)\n", "\n", "    loss = out.sum()\n", "    loss.backward()\n", "print(\"\\n拆解执行 CUDA kernels:\")\n", "print(prof.key_averages().table(sort_by=\"cuda_time_total\", row_limit=15))\n"]}, {"cell_type": "code", "execution_count": 16, "id": "147a4877", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "                                                   Name    Self CPU %      Self CPU   CPU total %     CPU total  CPU time avg     Self CUDA   Self CUDA %    CUDA total  CUDA time avg    # of Calls  \n", "-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "    autograd::engine::evaluate_function: AddmmBackward0         0.81%      39.809us         9.44%     464.657us     116.164us       0.000us         0.00%      79.423us      19.856us             4  \n", "                                              aten::sum         2.09%     103.063us         2.98%     146.580us      24.430us      47.392us        20.97%      47.392us       7.899us             6  \n", "                                         AddmmBackward0         0.67%      33.015us         7.03%     345.975us      86.494us       0.000us         0.00%      42.143us      10.536us             4  \n", "                                               aten::mm         3.63%     178.509us         5.46%     268.893us      38.413us      42.143us        18.65%      42.143us       6.020us             7  \n", "void at::native::reduce_kernel<128, 4, at::native::R...         0.00%       0.000us         0.00%       0.000us       0.000us      37.280us        16.50%      37.280us       9.320us             4  \n", "autograd::engine::evaluate_function: ScaledDotProduc...         0.27%      13.043us         5.53%     272.374us     272.374us       0.000us         0.00%      31.712us      31.712us             1  \n", "            ScaledDotProductEfficientAttentionBackward0         0.54%      26.406us         5.27%     259.331us     259.331us       0.000us         0.00%      31.712us      31.712us             1  \n", "aten::_scaled_dot_product_efficient_attention_backwa...         0.69%      33.903us         4.73%     232.925us     232.925us       0.000us         0.00%      31.712us      31.712us             1  \n", "                    aten::_efficient_attention_backward         0.93%      45.702us         3.76%     185.225us     185.225us      22.592us        10.00%      31.712us      31.712us             1  \n", "                                            aten::copy_         2.32%     114.285us         5.25%     258.326us      17.222us      31.262us        13.83%      31.262us       2.084us            15  \n", "                                            aten::clone         0.78%      38.455us         6.91%     340.169us      28.347us       0.000us         0.00%      27.358us       2.280us            12  \n", "void at::native::elementwise_kernel<128, 2, at::nati...         0.00%       0.000us         0.00%       0.000us       0.000us      27.358us        12.11%      27.358us       2.280us            12  \n", "fmha_cutlassB_f32_aligned_64x64_k32_dropout_sm80(PyT...         0.00%       0.000us         0.00%       0.000us       0.000us      22.592us        10.00%      22.592us      22.592us             1  \n", "void cutlass::<PERSON>el<cutlass_80_simt_sgemm_64x64_8x5...         0.00%       0.000us         0.00%       0.000us       0.000us      19.712us         8.72%      19.712us       4.928us             4  \n", "                                           aten::linear         1.19%      58.746us        45.21%       2.225ms     556.239us       0.000us         0.00%      18.528us       4.632us             4  \n", "                                            aten::addmm        27.54%       1.356ms        42.12%       2.073ms     518.295us      18.528us         8.20%      18.528us       4.632us             4  \n", "sm80_xmma_gemm_f32f32_f32f32_f32_tn_n_tilesize32x32x...         0.00%       0.000us         0.00%       0.000us       0.000us      18.528us         8.20%      18.528us       4.632us             4  \n", "autograd::engine::evaluate_function: NativeLayerNorm...         0.59%      29.238us         5.02%     247.201us     123.600us       0.000us         0.00%      17.824us       8.912us             2  \n", "                               NativeLayerNormBackward0         0.53%      25.911us         4.43%     217.963us     108.982us       0.000us         0.00%      17.824us       8.912us             2  \n", "                       aten::native_layer_norm_backward         0.92%      45.285us         3.90%     192.052us      96.026us      15.872us         7.02%      17.824us       8.912us             2  \n", "-------------------------------------------------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  ------------  \n", "Self CPU time total: 4.922ms\n", "Self CUDA time total: 225.981us\n", "\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "from torch.profiler import profile, ProfilerActivity\n", "\n", "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "layer = nn.TransformerEncoderLayer(d_model=64, nhead=4, dim_feedforward=128).to(device)\n", "x = torch.randn(20, 32, 64, device=device)\n", "with profile(activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA], record_shapes=True) as prof:\n", "    out = layer(x)\n", "    loss = out.sum()\n", "    loss.backward()\n", "\n", "print(prof.key_averages().table(sort_by=\"cuda_time_total\", row_limit=20))\n"]}, {"cell_type": "markdown", "id": "05740630", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}}, "nbformat": 4, "nbformat_minor": 5}