"""层中心性能预测实验评估"""
import torch
import numpy as np
import pandas as pd
from typing import Dict, List
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.model_selection import cross_val_score

from features.advanced_extractor import LayerCentricPredictor
from models.simple_model import SimpleModel
from models.bert import BERT
from models.vit import ViT

class LayerCentricExperiment:
    """层中心性能预测实验"""
    
    def __init__(self):
        self.predictor = LayerCentricPredictor()
        self.results = {}
        
    def run_single_layer_analysis(self):
        """实验1：单层性能分析"""
        print("=== 实验1：单层性能分析 ===")
        
        # 测试不同类型的层
        test_layers = [
            (torch.nn.Linear(512, 256), (32, 512)),
            (torch.nn.Conv2d(3, 64, 3), (32, 3, 224, 224)),
            (torch.nn.MultiheadAttention(512, 8), (32, 100, 512)),
            (torch.nn.LayerNorm(512), (32, 100, 512))
        ]
        
        layer_analysis = []
        for layer, input_shape in test_layers:
            features = self.predictor.layer_analyzer.analyze_layer_computation(layer, input_shape)
            features['layer_type'] = type(layer).__name__
            layer_analysis.append(features)
        
        # 分析结果
        df = pd.DataFrame(layer_analysis)
        print(df)
        
        # 可视化
        self._plot_layer_characteristics(df)
        
        return df
    
    def run_cross_architecture_analysis(self):
        """实验2：跨架构泛化能力"""
        print("=== 实验2：跨架构泛化能力 ===")
        
        models = {
            'SimpleModel': SimpleModel(hidden_size=512, num_layers=6),
            'BERT': BERT(hidden_size=512, num_hidden_layers=6),
            'ViT': ViT(hidden_size=512, num_hidden_layers=6)
        }
        
        architecture_features = {}
        for name, model in models.items():
            if name == 'ViT':
                input_shape = (32, 3, 224, 224)
            else:
                input_shape = (32, 128, 512)
            
            features = self.predictor.extract_model_features(model, input_shape)
            architecture_features[name] = features
            
            print(f"\n{name} 特征:")
            print(f"  总层数: {len(features['layers'])}")
            print(f"  总FLOPs: {features['total_flops']:,}")
            print(f"  总参数: {features['total_params']:,}")
            print(f"  关键路径长度: {features['critical_path_length']}")
            print(f"  瓶颈层数: {len(features['parallelization_bottlenecks'])}")
        
        return architecture_features
    
    def run_distributed_scaling_analysis(self):
        """实验3：分布式扩展性分析"""
        print("=== 实验3：分布式扩展性分析 ===")
        
        # 测试不同的分布式配置
        gpu_configs = [2, 4, 8, 16, 32]
        bandwidth_configs = [25, 50, 100, 200]  # GB/s
        
        model = SimpleModel(hidden_size=1024, num_layers=12)
        model_features = self.predictor.extract_model_features(model, (32, 128, 1024))
        
        scaling_results = []
        
        for num_gpus in gpu_configs:
            for bandwidth in bandwidth_configs:
                config = {
                    'num_gpus': num_gpus,
                    'bandwidth_gbps': bandwidth,
                    'latency_us': 10,  # 10us latency
                    'batch_size': 32
                }
                
                predictions = self.predictor.predict_distributed_performance(
                    model_features, config
                )
                
                result = {
                    'num_gpus': num_gpus,
                    'bandwidth_gbps': bandwidth,
                    'dp_efficiency': predictions['data_parallel_efficiency'],
                    'theoretical_speedup': num_gpus * predictions['data_parallel_efficiency']
                }
                scaling_results.append(result)
        
        df = pd.DataFrame(scaling_results)
        
        # 可视化扩展性
        self._plot_scaling_analysis(df)
        
        return df
    
    def run_feature_importance_analysis(self):
        """实验4：特征重要性分析"""
        print("=== 实验4：特征重要性分析 ===")
        
        # 生成合成数据集
        synthetic_data = self._generate_synthetic_dataset(1000)
        
        # 训练随机森林并分析特征重要性
        from sklearn.ensemble import RandomForestRegressor
        
        feature_cols = [col for col in synthetic_data.columns 
                       if col not in ['performance_score']]
        X = synthetic_data[feature_cols]
        y = synthetic_data['performance_score']
        
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X, y)
        
        # 特征重要性
        importance_df = pd.DataFrame({
            'feature': feature_cols,
            'importance': rf.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("特征重要性排序:")
        print(importance_df.head(10))
        
        # 可视化
        self._plot_feature_importance(importance_df)
        
        return importance_df
    
    def _generate_synthetic_dataset(self, n_samples: int) -> pd.DataFrame:
        """生成合成数据集用于特征重要性分析"""
        np.random.seed(42)
        
        data = []
        for _ in range(n_samples):
            # 随机生成模型配置
            config = {
                'total_flops': np.random.randint(1e6, 1e10),
                'total_params': np.random.randint(1e5, 1e8),
                'num_layers': np.random.randint(6, 48),
                'avg_parallelizability': np.random.uniform(0.3, 0.9),
                'memory_intensity': np.random.uniform(0.1, 2.0),
                'critical_path_length': np.random.randint(6, 48),
                'bottleneck_ratio': np.random.uniform(0.0, 0.3)
            }
            
            # 合成性能分数（基于特征的线性组合 + 噪声）
            performance = (
                -0.3 * np.log10(config['total_flops']) +
                0.4 * config['avg_parallelizability'] +
                -0.2 * config['memory_intensity'] +
                -0.1 * config['bottleneck_ratio'] +
                np.random.normal(0, 0.1)
            )
            
            config['performance_score'] = performance
            data.append(config)
        
        return pd.DataFrame(data)
    
    def _plot_layer_characteristics(self, df: pd.DataFrame):
        """可视化层特征"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # FLOPs vs 并行度
        axes[0, 0].scatter(df['flops'], df['parallelizability'])
        axes[0, 0].set_xlabel('FLOPs')
        axes[0, 0].set_ylabel('Parallelizability')
        axes[0, 0].set_title('FLOPs vs Parallelizability')
        
        # 内存强度分布
        axes[0, 1].hist(df['memory_intensity'], bins=10)
        axes[0, 1].set_xlabel('Memory Intensity')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Memory Intensity Distribution')
        
        # 参数量 vs 梯度大小
        axes[1, 0].scatter(df['param_count'], df['gradient_size'])
        axes[1, 0].set_xlabel('Parameter Count')
        axes[1, 0].set_ylabel('Gradient Size')
        axes[1, 0].set_title('Parameters vs Gradient Size')
        
        # 层类型分布
        layer_counts = df['layer_type'].value_counts()
        axes[1, 1].pie(layer_counts.values, labels=layer_counts.index, autopct='%1.1f%%')
        axes[1, 1].set_title('Layer Type Distribution')
        
        plt.tight_layout()
        plt.savefig('layer_characteristics.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def _plot_scaling_analysis(self, df: pd.DataFrame):
        """可视化扩展性分析"""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 数据并行效率 vs GPU数量
        for bandwidth in df['bandwidth_gbps'].unique():
            subset = df[df['bandwidth_gbps'] == bandwidth]
            axes[0].plot(subset['num_gpus'], subset['dp_efficiency'], 
                        marker='o', label=f'{bandwidth} GB/s')
        
        axes[0].set_xlabel('Number of GPUs')
        axes[0].set_ylabel('Data Parallel Efficiency')
        axes[0].set_title('Data Parallel Efficiency vs GPU Count')
        axes[0].legend()
        axes[0].grid(True)
        
        # 理论加速比
        for bandwidth in df['bandwidth_gbps'].unique():
            subset = df[df['bandwidth_gbps'] == bandwidth]
            axes[1].plot(subset['num_gpus'], subset['theoretical_speedup'], 
                        marker='s', label=f'{bandwidth} GB/s')
        
        # 理想线性加速比
        axes[1].plot(df['num_gpus'].unique(), df['num_gpus'].unique(), 
                    'k--', label='Ideal Linear')
        
        axes[1].set_xlabel('Number of GPUs')
        axes[1].set_ylabel('Theoretical Speedup')
        axes[1].set_title('Theoretical Speedup vs GPU Count')
        axes[1].legend()
        axes[1].grid(True)
        
        plt.tight_layout()
        plt.savefig('scaling_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def _plot_feature_importance(self, importance_df: pd.DataFrame):
        """可视化特征重要性"""
        plt.figure(figsize=(10, 6))
        
        top_features = importance_df.head(10)
        plt.barh(range(len(top_features)), top_features['importance'])
        plt.yticks(range(len(top_features)), top_features['feature'])
        plt.xlabel('Feature Importance')
        plt.title('Top 10 Feature Importance for Performance Prediction')
        plt.gca().invert_yaxis()
        
        plt.tight_layout()
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    """运行完整的实验套件"""
    experiment = LayerCentricExperiment()
    
    # 运行所有实验
    print("开始层中心性能预测实验...")
    
    # 实验1：单层分析
    layer_results = experiment.run_single_layer_analysis()
    
    # 实验2：跨架构分析
    arch_results = experiment.run_cross_architecture_analysis()
    
    # 实验3：分布式扩展性
    scaling_results = experiment.run_distributed_scaling_analysis()
    
    # 实验4：特征重要性
    importance_results = experiment.run_feature_importance_analysis()
    
    print("\n=== 实验完成 ===")
    print("结果已保存到图片文件中")

if __name__ == "__main__":
    main()