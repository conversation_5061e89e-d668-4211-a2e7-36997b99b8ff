{"cells": [{"cell_type": "code", "execution_count": 1, "id": "dc66a5d6-0b93-40d2-bbef-582e458ae924", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.profiler import profile, record_function, ProfilerActivity"]}, {"cell_type": "code", "execution_count": 2, "id": "9a67bb6a-4b38-4f09-8366-3628d8e1e71d", "metadata": {}, "outputs": [], "source": ["# 检查是否有可用的 GPU\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "# 改造后的简单神经网络，包含超参数：batch_size, seq_len, hidden_size, num_layers, num_heads, dropout\n", "class SimpleModel(nn.Module):\n", "    def __init__(self, input_size=100, seq_len=10, hidden_size=64, num_layers=2, num_heads=4, dropout=0.1, num_classes=10):\n", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.input_size = input_size\n", "        self.seq_len = seq_len\n", "        self.hidden_size = hidden_size\n", "        self.num_layers = num_layers\n", "        self.num_heads = num_heads\n", "        self.dropout = dropout\n", "        self.num_classes = num_classes\n", "\n", "        # 假设输入为(batch, seq_len, input_size)\n", "        self.embedding = nn.Linear(input_size, hidden_size)\n", "        encoder_layer = nn.TransformerEncoderLayer(\n", "            d_model=hidden_size, \n", "            nhead=num_heads, \n", "            dim_feedforward=hidden_size*2, \n", "            dropout=dropout,\n", "            activation='relu'\n", "        )\n", "        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)\n", "        self.dropout_layer = nn.Dropout(dropout)\n", "        self.classifier = nn.Linear(hidden_size, num_classes)\n", "\n", "    def forward(self, x):\n", "        # x: (batch, seq_len, input_size)\n", "        x = self.embedding(x)  # (batch, seq_len, hidden_size)\n", "        x = x.permute(1, 0, 2)  # (seq_len, batch, hidden_size) 适配Transformer\n", "        x = self.transformer_encoder(x)  # (seq_len, batch, hidden_size)\n", "        x = x.mean(dim=0)  # (batch, hidden_size) 取所有时间步的均值\n", "        x = self.dropout_layer(x)\n", "        x = self.classifier(x)  # (batch, num_classes)\n", "        return x"]}, {"cell_type": "code", "execution_count": 3, "id": "b870039c", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/transformer.py:307: UserWarning: enable_nested_tensor is True, but self.use_nested_tensor is False because encoder_layer.self_attn.batch_first was not True(use batch_first for better inference performance)\n", "  warnings.warn(f\"enable_nested_tensor is True, but self.use_nested_tensor is False because {why_not_sparsity_fast_path}\")\n"]}], "source": ["# 定义模型特征枚举类\n", "from enum import Enum\n", "class ModelFeature(Enum):\n", "    Linear = 1\n", "    Conv2d = 2\n", "    Conv1d = 3\n", "    Dropout = 4\n", "    ReLU = 5\n", "    Sigmoid = 6\n", "    Tanh = 7\n", "    Softmax = 8\n", "    LayerNorm = 9\n", "    NonDynamicallyQuantizableLinear = 10\n", "\n", "    def __str__(self):\n", "        return self.name\n", "    \n", "    @staticmethod\n", "    def list_features():\n", "        return [feature.name for feature in ModelFeature]\n", "\n", "# 定义模型特征提取器\n", "class ModelFeatureExtractor:\n", "    def extract_features(self, model):\n", "        \"\"\"提取模型的层特征\"\"\"\n", "        features = []\n", "        # 统计各类型层的数量\n", "        layer_counts = {feature.name: 0 for feature in ModelFeature}\n", "        total_layers = 0\n", "        for name, module in model.named_modules():\n", "            # print(name, module)\n", "            if len(list(module.children())) == 0:  # 叶子节点（实际层）\n", "                # 提取 module 的名字，如 Linear(in_features=100, out_features=50, bias=True)，提取Linear\n", "                module_name = str(module) \n", "                module_name = module_name.split('(')[0]\n", "                # print(module_name)\n", "                # print(ModelFeature.list_features())\n", "                total_layers += 1\n", "                if module_name in ModelFeature.list_features():\n", "                    layer_counts[module_name] += 1\n", "                else:\n", "                    print(\"[ModelFeatureExtractor] <Error> Unknown layer type: \", module_name)\n", "        # 构建特征向量\n", "        features = [total_layers]  # 总层数\n", "        for layer_type in ModelFeature.list_features():\n", "            features.append(layer_counts[layer_type])\n", "        \n", "        # 添加模型参数数量\n", "        total_params = sum(p.numel() for p in model.parameters())\n", "        features.append(total_params)\n", "        \n", "        # 添加可训练参数数量\n", "        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "        features.append(trainable_params)\n", "        \n", "        # print(\"[ModelFeatureExtractor] <Info> Features: \", features)\n", "        # print(\"[ModelFeatureExtractor] <Info> layer_counts: \", layer_counts)\n", "        # print(\"[ModelFeatureExtractor] <Info> total_params: \", total_params)\n", "        # print(\"[ModelFeatureExtractor] <Info> trainable_params: \", trainable_params)\n", "        return features, layer_counts, total_params, trainable_params \n", "\n", "model_architecture_features = ModelFeatureExtractor().extract_features(SimpleModel())\n"]}, {"cell_type": "code", "execution_count": 4, "id": "87f53b55", "metadata": {}, "outputs": [], "source": ["# 推理函数\n", "def benchmark_model(model, batch_size, seq_len, hidden_size, num_iterations=5):\n", "    \"\"\"测试模型的性能\"\"\"\n", "    model.eval()\n", "    \n", "    # 创建输入数据\n", "    inputs = torch.randn(batch_size, seq_len, hidden_size).to(device)\n", "    \n", "    # 预热\n", "    with torch.no_grad():\n", "        for _ in range(3):\n", "            _ = model(inputs)\n", "    \n", "    # 同步 GPU\n", "    if torch.cuda.is_available():\n", "        torch.cuda.synchronize()\n", "    \n", "    # 测量推理时间\n", "    start_time = time.time()\n", "    with torch.no_grad():\n", "        for _ in range(num_iterations):\n", "            _ = model(inputs)\n", "    \n", "    if torch.cuda.is_available():\n", "        torch.cuda.synchronize()\n", "    \n", "    end_time = time.time()\n", "    \n", "    # 计算平均推理时间（毫秒）\n", "    avg_inference_time = (end_time - start_time) / num_iterations * 1000\n", "    \n", "    # 测量内存使用\n", "    if torch.cuda.is_available():\n", "        memory_allocated = torch.cuda.memory_allocated() / 1024 / 1024  # MB\n", "        memory_reserved = torch.cuda.memory_reserved() / 1024 / 1024    # MB\n", "    else:\n", "        memory_allocated = 0\n", "        memory_reserved = 0\n", "    \n", "    return avg_inference_time, memory_allocated, memory_reserved\n", "\n", "\n", "# 训练函数\n", "def train_model(model, batch_size, seq_len, hidden_size, num_epochs=5):\n", "    \"\"\"训练模型并测量训练时间\"\"\"\n", "    model.train()\n", "    \n", "    # 创建训练数据\n", "    inputs = torch.randn(batch_size, seq_len, hidden_size).to(device)\n", "    targets = torch.randint(0, 10, (batch_size,)).to(device)\n", "    \n", "    # 损失函数和优化器\n", "    criterion = nn.CrossEntropyLoss()\n", "    optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "    \n", "    # 预热\n", "    for _ in range(3):\n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = criterion(outputs, targets)\n", "        loss.backward()\n", "        optimizer.step()\n", "    \n", "    # 测量训练时间\n", "    if torch.cuda.is_available():\n", "        torch.cuda.synchronize()\n", "    \n", "    start_time = time.time()\n", "    for epoch in range(num_epochs):\n", "        optimizer.zero_grad()\n", "        outputs = model(inputs)\n", "        loss = criterion(outputs, targets)\n", "        loss.backward()\n", "        optimizer.step()\n", "    \n", "    if torch.cuda.is_available():\n", "        torch.cuda.synchronize()\n", "    \n", "    end_time = time.time()\n", "    \n", "    # 计算平均训练时间（毫秒）\n", "    avg_training_time = (end_time - start_time) / num_epochs * 1000\n", "    \n", "    return avg_training_time\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "b2fa9ceb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始收集训练数据...\n", "总共需要测试 4096 种超参数组合\n"]}, {"name": "stderr", "output_type": "stream", "text": ["测试进度:   0%|                                                                                                                | 0/4096 [00:00<?, ?it/s]/home/<USER>/anaconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/transformer.py:307: UserWarning: enable_nested_tensor is True, but self.use_nested_tensor is False because encoder_layer.self_attn.batch_first was not True(use batch_first for better inference performance)\n", "  warnings.warn(f\"enable_nested_tensor is True, but self.use_nested_tensor is False because {why_not_sparsity_fast_path}\")\n", "测试进度:   0%|                                                                                                      | 1/4096 [00:02<2:45:41,  2.43s/it]/home/<USER>/anaconda3/envs/py38/lib/python3.8/site-packages/torch/nn/modules/transformer.py:307: UserWarning: enable_nested_tensor is True, but self.use_nested_tensor is False because encoder_layer.self_attn.batch_first was not True(use batch_first for better inference performance)\n", "  warnings.warn(f\"enable_nested_tensor is True, but self.use_nested_tensor is False because {why_not_sparsity_fast_path}\")\n", "测试进度:   0%|▍                                                                                                      | 19/4096 [00:03<06:05, 11.14it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["测试进度: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████| 4096/4096 [56:28<00:00,  1.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["总共收集了 4096 条数据\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["import numpy as np\n", "from tqdm import tqdm\n", "import itertools\n", "import pandas as pd\n", "import time\n", "\n", "# 枚举超参数，如 batch_size, seq_len, hidden_size, num_layers, num_heads, dropout，分别训练获取数据\n", "# 定义超参数范围\n", "hyperparameter_ranges = {\n", "    'batch_size': [8, 16, 32, 64],\n", "    'seq_len': [64, 128, 256, 512],\n", "    'hidden_size': [128, 256, 512, 1024],\n", "    'num_layers': [2, 4, 6, 8],\n", "    'num_heads': [4, 8, 16, 32],\n", "    'dropout': [0.0, 0.1, 0.2, 0.3]\n", "}\n", "\n", "# 枚举超参数并收集数据\n", "def collect_training_data():\n", "    \"\"\"收集不同超参数配置下的训练数据\"\"\"\n", "    print(\"开始收集训练数据...\")\n", "    \n", "    # 创建结果存储列表\n", "    results = []\n", "    \n", "    # 生成所有超参数组合\n", "    param_names = list(hyperparameter_ranges.keys())\n", "    param_values = list(hyperparameter_ranges.values())\n", "    \n", "    # 计算总组合数\n", "    total_combinations = np.prod([len(vals) for vals in param_values])\n", "    print(f\"总共需要测试 {total_combinations} 种超参数组合\")\n", "    \n", "    # 创建进度条\n", "    pbar = tqdm(total=total_combinations, desc=\"测试进度\")\n", "    \n", "    # 遍历所有超参数组合\n", "    for i, param_combination in enumerate(itertools.product(*param_values)):\n", "        # 创建参数字典\n", "        params = dict(zip(param_names, param_combination))\n", "        # print(f\"[Collect Traing Data]<info>: 第{i}轮执行，执行参数 {params}\")\n", "        # if i > 100:\n", "        #     break\n", "        try:\n", "            # 使用超参数创建模型\n", "            model = SimpleModel(\n", "                input_size=int(params['hidden_size']),   # 与后续输入张量最后一维一致\n", "                seq_len=int(params['seq_len']),\n", "                hidden_size=int(params['hidden_size']),\n", "                num_layers=int(params['num_layers']),\n", "                num_heads=int(params['num_heads']),\n", "                dropout=float(params['dropout']),\n", "                num_classes=10,\n", "            ).to(device)\n", "            \n", "            # 提取模型特征\n", "            feature_extractor = ModelFeatureExtractor()\n", "            features, layer_counts, total_params, trainable_params = feature_extractor.extract_features(model)\n", "            \n", "            # 测试推理性能\n", "            inference_time, memory_allocated, memory_reserved = benchmark_model(\n", "                model, params['batch_size'], params['seq_len'], params['hidden_size']\n", "            )\n", "            \n", "            # 测试训练性能\n", "            training_time = train_model(\n", "                model, params['batch_size'], params['seq_len'], params['hidden_size']\n", "            )\n", "            \n", "            # 存储结果\n", "            result = {\n", "                **params,  # 超参数\n", "                'total_layers': features[0],\n", "                'total_params': total_params,\n", "                'trainable_params': trainable_params,\n", "                'inference_time_ms': inference_time,\n", "                'training_time_ms': training_time,\n", "                'memory_allocated_mb': memory_allocated,\n", "                'memory_reserved_mb': memory_reserved\n", "            }\n", "            \n", "            # 添加层类型统计\n", "            for layer_type, count in layer_counts.items():\n", "                result[f'layer_{layer_type}'] = count\n", "            \n", "            results.append(result)\n", "            \n", "            # 清理内存\n", "            del model\n", "            if torch.cuda.is_available():\n", "                torch.cuda.empty_cache()\n", "            \n", "        except Exception as e:\n", "            print(f\"测试参数 {params} 时出错: {e}\")\n", "            continue\n", "        \n", "        pbar.update(1)\n", "    \n", "    pbar.close()\n", "    \n", "    # 转换为 DataFrame\n", "    df = pd.DataFrame(results)\n", "\n", "    print(f\"总共收集了 {len(results)} 条数据\")\n", "    \n", "    return df\n", "\n", "# 收集训练数据\n", "df = collect_training_data()"]}, {"cell_type": "code", "execution_count": 6, "id": "82e9df6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据分析结果 ===\n", "总数据条数: 4096\n", "超参数组合数: 4096\n", "\n", "推理时间统计 (ms):\n", "  平均值: 20.73\n", "  最小值: 0.66\n", "  最大值: 406.78\n", "\n", "训练时间统计 (ms):\n", "  平均值: 69.16\n", "  最小值: 2.96\n", "  最大值: 1309.58\n", "\n", "内存使用统计 (MB):\n", "  平均分配内存: 84.34\n", "  平均保留内存: 261.16\n", "\n", "模型参数统计:\n", "  平均参数数: 14,306,250\n", "  最小参数数: 282,762\n", "  最大参数数: 68,258,826\n", "\n", "相关性分析:\n", "                   total_params    num_layers   hidden_size  \\\n", "total_params           1.000000  3.184469e-01  8.523041e-01   \n", "num_layers             0.318447  1.000000e+00 -1.511245e-16   \n", "hidden_size            0.852304 -1.511245e-16  1.000000e+00   \n", "inference_time_ms      0.489530  2.251824e-01  3.970170e-01   \n", "training_time_ms       0.472215  2.366622e-01  3.783641e-01   \n", "\n", "                   inference_time_ms  training_time_ms  \n", "total_params                0.489530          0.472215  \n", "num_layers                  0.225182          0.236662  \n", "hidden_size                 0.397017          0.378364  \n", "inference_time_ms           1.000000          0.996994  \n", "training_time_ms            0.996994          1.000000  \n", "\n", "按推理时间排序:\n", "      inference_time_ms\n", "1034           0.657797\n", "1024           0.659227\n", "1028           0.660801\n", "4              0.662565\n", "1035           0.664186\n", "...                 ...\n", "4089         363.833714\n", "4094         406.175613\n", "4095         406.242657\n", "4093         406.334448\n", "4092         406.777477\n", "\n", "[4096 rows x 1 columns]\n", "\n", "按训练时间排序:\n", "      training_time_ms\n", "1032          2.959013\n", "1028          2.963257\n", "256           3.013325\n", "1536          3.024769\n", "1029          3.169727\n", "...                ...\n", "4090       1121.062183\n", "4092       1240.296650\n", "4094       1308.299828\n", "4093       1308.579969\n", "4095       1309.584761\n", "\n", "[4096 rows x 1 columns]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>batch_size</th>\n", "      <th>seq_len</th>\n", "      <th>hidden_size</th>\n", "      <th>num_layers</th>\n", "      <th>num_heads</th>\n", "      <th>dropout</th>\n", "      <th>total_layers</th>\n", "      <th>total_params</th>\n", "      <th>trainable_params</th>\n", "      <th>inference_time_ms</th>\n", "      <th>...</th>\n", "      <th>layer_Linear</th>\n", "      <th>layer_Conv2d</th>\n", "      <th>layer_Conv1d</th>\n", "      <th>layer_Dropout</th>\n", "      <th>layer_ReLU</th>\n", "      <th>layer_Sigmoid</th>\n", "      <th>layer_Tanh</th>\n", "      <th>layer_Softmax</th>\n", "      <th>layer_LayerNorm</th>\n", "      <th>layer_NonDynamicallyQuantizableLinear</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8</td>\n", "      <td>64</td>\n", "      <td>128</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>0.0</td>\n", "      <td>19</td>\n", "      <td>282762</td>\n", "      <td>282762</td>\n", "      <td>0.681400</td>\n", "      <td>...</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8</td>\n", "      <td>64</td>\n", "      <td>128</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>0.1</td>\n", "      <td>19</td>\n", "      <td>282762</td>\n", "      <td>282762</td>\n", "      <td>0.675440</td>\n", "      <td>...</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>8</td>\n", "      <td>64</td>\n", "      <td>128</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>0.2</td>\n", "      <td>19</td>\n", "      <td>282762</td>\n", "      <td>282762</td>\n", "      <td>0.681210</td>\n", "      <td>...</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>8</td>\n", "      <td>64</td>\n", "      <td>128</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "      <td>0.3</td>\n", "      <td>19</td>\n", "      <td>282762</td>\n", "      <td>282762</td>\n", "      <td>0.900316</td>\n", "      <td>...</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>8</td>\n", "      <td>64</td>\n", "      <td>128</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>0.0</td>\n", "      <td>19</td>\n", "      <td>282762</td>\n", "      <td>282762</td>\n", "      <td>0.662565</td>\n", "      <td>...</td>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4091</th>\n", "      <td>64</td>\n", "      <td>512</td>\n", "      <td>1024</td>\n", "      <td>8</td>\n", "      <td>16</td>\n", "      <td>0.3</td>\n", "      <td>67</td>\n", "      <td>68258826</td>\n", "      <td>68258826</td>\n", "      <td>363.460445</td>\n", "      <td>...</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4092</th>\n", "      <td>64</td>\n", "      <td>512</td>\n", "      <td>1024</td>\n", "      <td>8</td>\n", "      <td>32</td>\n", "      <td>0.0</td>\n", "      <td>67</td>\n", "      <td>68258826</td>\n", "      <td>68258826</td>\n", "      <td>406.777477</td>\n", "      <td>...</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4093</th>\n", "      <td>64</td>\n", "      <td>512</td>\n", "      <td>1024</td>\n", "      <td>8</td>\n", "      <td>32</td>\n", "      <td>0.1</td>\n", "      <td>67</td>\n", "      <td>68258826</td>\n", "      <td>68258826</td>\n", "      <td>406.334448</td>\n", "      <td>...</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4094</th>\n", "      <td>64</td>\n", "      <td>512</td>\n", "      <td>1024</td>\n", "      <td>8</td>\n", "      <td>32</td>\n", "      <td>0.2</td>\n", "      <td>67</td>\n", "      <td>68258826</td>\n", "      <td>68258826</td>\n", "      <td>406.175613</td>\n", "      <td>...</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4095</th>\n", "      <td>64</td>\n", "      <td>512</td>\n", "      <td>1024</td>\n", "      <td>8</td>\n", "      <td>32</td>\n", "      <td>0.3</td>\n", "      <td>67</td>\n", "      <td>68258826</td>\n", "      <td>68258826</td>\n", "      <td>406.242657</td>\n", "      <td>...</td>\n", "      <td>18</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "      <td>8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4096 rows × 23 columns</p>\n", "</div>"], "text/plain": ["      batch_size  seq_len  hidden_size  num_layers  num_heads  dropout  \\\n", "0              8       64          128           2          4      0.0   \n", "1              8       64          128           2          4      0.1   \n", "2              8       64          128           2          4      0.2   \n", "3              8       64          128           2          4      0.3   \n", "4              8       64          128           2          8      0.0   \n", "...          ...      ...          ...         ...        ...      ...   \n", "4091          64      512         1024           8         16      0.3   \n", "4092          64      512         1024           8         32      0.0   \n", "4093          64      512         1024           8         32      0.1   \n", "4094          64      512         1024           8         32      0.2   \n", "4095          64      512         1024           8         32      0.3   \n", "\n", "      total_layers  total_params  trainable_params  inference_time_ms  ...  \\\n", "0               19        282762            282762           0.681400  ...   \n", "1               19        282762            282762           0.675440  ...   \n", "2               19        282762            282762           0.681210  ...   \n", "3               19        282762            282762           0.900316  ...   \n", "4               19        282762            282762           0.662565  ...   \n", "...            ...           ...               ...                ...  ...   \n", "4091            67      68258826          68258826         363.460445  ...   \n", "4092            67      68258826          68258826         406.777477  ...   \n", "4093            67      68258826          68258826         406.334448  ...   \n", "4094            67      68258826          68258826         406.175613  ...   \n", "4095            67      68258826          68258826         406.242657  ...   \n", "\n", "      layer_Linear  layer_Conv2d  layer_Conv1d  layer_Dropout  layer_ReLU  \\\n", "0                6             0             0              7           0   \n", "1                6             0             0              7           0   \n", "2                6             0             0              7           0   \n", "3                6             0             0              7           0   \n", "4                6             0             0              7           0   \n", "...            ...           ...           ...            ...         ...   \n", "4091            18             0             0             25           0   \n", "4092            18             0             0             25           0   \n", "4093            18             0             0             25           0   \n", "4094            18             0             0             25           0   \n", "4095            18             0             0             25           0   \n", "\n", "      layer_Sigmoid  layer_Tanh  layer_Softmax  layer_LayerNorm  \\\n", "0                 0           0              0                4   \n", "1                 0           0              0                4   \n", "2                 0           0              0                4   \n", "3                 0           0              0                4   \n", "4                 0           0              0                4   \n", "...             ...         ...            ...              ...   \n", "4091              0           0              0               16   \n", "4092              0           0              0               16   \n", "4093              0           0              0               16   \n", "4094              0           0              0               16   \n", "4095              0           0              0               16   \n", "\n", "      layer_NonDynamicallyQuantizableLinear  \n", "0                                         2  \n", "1                                         2  \n", "2                                         2  \n", "3                                         2  \n", "4                                         2  \n", "...                                     ...  \n", "4091                                      8  \n", "4092                                      8  \n", "4093                                      8  \n", "4094                                      8  \n", "4095                                      8  \n", "\n", "[4096 rows x 23 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# 数据分析函数\n", "def analyze_results(df):\n", "    \"\"\"分析收集到的数据\"\"\"\n", "    print(\"\\n=== 数据分析结果 ===\")\n", "    \n", "    # 基本统计\n", "    print(f\"总数据条数: {len(df)}\")\n", "    print(f\"超参数组合数: {len(df)}\")\n", "    \n", "    # 性能统计\n", "    print(f\"\\n推理时间统计 (ms):\")\n", "    print(f\"  平均值: {df['inference_time_ms'].mean():.2f}\")\n", "    print(f\"  最小值: {df['inference_time_ms'].min():.2f}\")\n", "    print(f\"  最大值: {df['inference_time_ms'].max():.2f}\")\n", "    \n", "    print(f\"\\n训练时间统计 (ms):\")\n", "    print(f\"  平均值: {df['training_time_ms'].mean():.2f}\")\n", "    print(f\"  最小值: {df['training_time_ms'].min():.2f}\")\n", "    print(f\"  最大值: {df['training_time_ms'].max():.2f}\")\n", "    \n", "    print(f\"\\n内存使用统计 (MB):\")\n", "    print(f\"  平均分配内存: {df['memory_allocated_mb'].mean():.2f}\")\n", "    print(f\"  平均保留内存: {df['memory_reserved_mb'].mean():.2f}\")\n", "    \n", "    # 参数数量统计\n", "    print(f\"\\n模型参数统计:\")\n", "    print(f\"  平均参数数: {df['total_params'].mean():,.0f}\")\n", "    print(f\"  最小参数数: {df['total_params'].min():,.0f}\")\n", "    print(f\"  最大参数数: {df['total_params'].max():,.0f}\")\n", "    \n", "    # 相关性分析\n", "    print(f\"\\n相关性分析:\")\n", "    correlation_cols = ['total_params', 'num_layers', 'hidden_size', 'inference_time_ms', 'training_time_ms']\n", "    correlation_matrix = df[correlation_cols].corr()\n", "    print(correlation_matrix)\n", "\n", "    # 输出推理时间和训练时间，并按从小到大排序\n", "    print(\"\\n按推理时间排序:\")\n", "    print(df[['inference_time_ms']].sort_values('inference_time_ms'))\n", "\n", "    print(\"\\n按训练时间排序:\")\n", "    print(df[['training_time_ms']].sort_values('training_time_ms'))\n", "\n", "    \n", "    return df\n", "\n", "# 分析结果\n", "analyze_results(df)"]}, {"cell_type": "code", "execution_count": 10, "id": "c9cf6be3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fitting 3 folds for each of 108 candidates, totalling 324 fits\n", "最佳参数: {'max_depth': None, 'min_samples_leaf': 1, 'min_samples_split': 2, 'n_estimators': 200}\n", "\n", "目标: inference_time_ms\n", "MSE:  0.4630\n", "RMSE: 0.6805\n", "MAE:  0.2766\n", "R^2:  0.9997\n", "CV R^2:  mean 0.9995 ± 0.0001\n", "CV RMSE: mean 0.8413 ± 0.1279\n", "\n", "重要特征(Top 15):\n", "batch_size                               0.351417\n", "seq_len                                  0.306202\n", "trainable_params                         0.146486\n", "total_params                             0.121602\n", "num_heads                                0.021186\n", "layer_NonDynamicallyQuantizableLinear    0.009969\n", "layer_LayerNorm                          0.009482\n", "total_layers                             0.008646\n", "num_layers                               0.008306\n", "layer_Linear                             0.008272\n", "layer_Dropout                            0.007956\n", "hidden_size                              0.000363\n", "dropout                                  0.000112\n", "layer_Conv2d                             0.000000\n", "layer_Conv1d                             0.000000\n", "dtype: float64\n", "预测结果 vs 真实结果（前100组）：\n", "预测值          真实值         \n", "1.2590       1.2156      \n", "2.4819       2.3654      \n", "23.1373      21.9282     \n", "1.7634       1.7500      \n", "10.4908      10.4166     \n", "33.1835      36.9287     \n", "23.0457      23.0337     \n", "46.1477      46.3453     \n", "1.7801       1.7884      \n", "24.6984      25.2240     \n", "45.0408      46.1250     \n", "1.3015       1.2568      \n", "5.5220       5.6315      \n", "15.5469      15.5143     \n", "36.0409      36.3457     \n", "0.8884       0.8681      \n", "20.1142      19.9488     \n", "49.1988      50.0603     \n", "12.6501      12.4679     \n", "0.7276       0.8224      \n", "76.2835      76.3851     \n", "2.3109       2.2925      \n", "4.9137       4.9254      \n", "14.2456      14.3763     \n", "8.1945       8.3622      \n", "1.6617       1.6778      \n", "1.8288       2.2643      \n", "8.4854       7.9721      \n", "10.5078      10.3204     \n", "18.2304      18.3536     \n", "11.9315      11.7759     \n", "38.8863      39.0993     \n", "2.2990       2.3028      \n", "1.7394       1.7209      \n", "1.7791       1.7802      \n", "20.1786      20.1630     \n", "75.3847      75.4397     \n", "5.9514       6.4470      \n", "2.3735       2.3644      \n", "2.8900       2.8876      \n", "1.7444       1.7320      \n", "0.8037       0.7856      \n", "7.0649       7.1696      \n", "3.1833       3.1512      \n", "8.2707       8.3906      \n", "17.4608      17.5727     \n", "6.3576       6.3022      \n", "8.8788       8.8181      \n", "27.9244      28.4410     \n", "44.2829      44.8236     \n", "1.2617       1.2603      \n", "1.2901       1.2519      \n", "6.4590       6.4422      \n", "2.4090       2.3947      \n", "12.7307      12.7940     \n", "6.1126       5.9313      \n", "163.9896     166.3774    \n", "16.5778      16.9443     \n", "1.0515       1.0391      \n", "6.5839       6.6175      \n", "0.8727       0.8636      \n", "4.7818       4.7112      \n", "2.8709       2.8419      \n", "3.2323       3.2327      \n", "10.9944      10.9061     \n", "23.0087      22.6573     \n", "11.9612      11.7742     \n", "61.1917      61.7331     \n", "0.7558       0.7137      \n", "13.2572      13.1395     \n", "0.8449       0.8153      \n", "1.6988       1.7666      \n", "11.9644      11.8167     \n", "400.9151     406.3344    \n", "4.6149       4.4939      \n", "4.3225       4.4132      \n", "2.5738       2.5692      \n", "30.3379      30.6568     \n", "1.7480       1.7310      \n", "15.4361      15.8482     \n", "2.2518       2.0951      \n", "1.2403       1.2371      \n", "0.8151       0.7504      \n", "3.6779       3.6304      \n", "23.0195      22.4538     \n", "5.1278       5.0696      \n", "2.4792       2.3951      \n", "132.7596     135.4130    \n", "30.8609      31.2335     \n", "89.6146      89.3721     \n", "13.2627      13.5550     \n", "1.2566       1.2789      \n", "5.2743       5.2997      \n", "3.0017       3.0392      \n", "1.2404       1.2194      \n", "2.3912       2.3971      \n", "32.7769      33.8834     \n", "2.9174       2.9252      \n", "10.1516      10.3046     \n", "1.7242       1.7037      \n", "Fitting 3 folds for each of 108 candidates, totalling 324 fits\n", "最佳参数: {'max_depth': None, 'min_samples_leaf': 1, 'min_samples_split': 2, 'n_estimators': 200}\n", "\n", "目标: training_time_ms\n", "MSE:  20.3553\n", "RMSE: 4.5117\n", "MAE:  1.6428\n", "R^2:  0.9988\n", "CV R^2:  mean 0.9989 ± 0.0002\n", "CV RMSE: mean 4.2297 ± 0.6601\n", "\n", "重要特征(Top 15):\n", "batch_size                               0.341234\n", "seq_len                                  0.266563\n", "trainable_params                         0.157806\n", "total_params                             0.147520\n", "num_heads                                0.031864\n", "layer_Dropout                            0.010424\n", "layer_LayerNorm                          0.009474\n", "num_layers                               0.008943\n", "total_layers                             0.008459\n", "layer_NonDynamicallyQuantizableLinear    0.008304\n", "layer_Linear                             0.008207\n", "hidden_size                              0.000613\n", "dropout                                  0.000588\n", "layer_Conv2d                             0.000000\n", "layer_Conv1d                             0.000000\n", "dtype: float64\n", "预测结果 vs 真实结果（前100组）：\n", "预测值          真实值         \n", "6.4409       6.6072      \n", "13.0035      13.2494     \n", "75.9138      76.4783     \n", "8.5065       11.5552     \n", "34.1695      34.1286     \n", "119.4072     136.7863    \n", "77.8419      78.8352     \n", "147.7017     148.8176    \n", "8.5383       8.5585      \n", "82.4607      84.4038     \n", "140.2083     139.1074    \n", "4.4920       4.2053      \n", "18.8934      19.1996     \n", "50.9544      50.8495     \n", "128.9106     130.3635    \n", "4.1969       3.4925      \n", "61.7323      61.3208     \n", "188.5839     192.1786    \n", "42.3887      42.3435     \n", "3.5732       4.0538      \n", "234.2638     235.6787    \n", "11.2213      10.4273     \n", "17.3956      17.2693     \n", "49.3203      51.2101     \n", "29.3164      29.4173     \n", "5.8197       5.9176      \n", "8.6010       8.0931      \n", "29.7275      27.8744     \n", "37.0950      37.3636     \n", "63.1194      66.6686     \n", "38.8695      40.5233     \n", "131.5520     133.9670    \n", "11.1690      10.4458     \n", "9.4975       7.9777      \n", "8.5250       8.7859      \n", "64.9532      65.2371     \n", "234.5912     234.3746    \n", "24.3516      25.2365     \n", "11.8193      9.6915      \n", "10.8957      11.2042     \n", "8.8750       7.9192      \n", "3.9997       3.8608      \n", "23.8559      23.5821     \n", "11.7616      11.8593     \n", "29.5598      29.9929     \n", "58.3825      59.2459     \n", "21.9006      22.5216     \n", "34.0377      33.9026     \n", "95.3527      92.2376     \n", "146.5414     148.2661    \n", "6.7769       8.3114      \n", "4.5169       4.5440      \n", "22.1679      22.9009     \n", "9.5355       9.5041      \n", "42.7726      42.9684     \n", "20.8915      20.9311     \n", "468.7547     473.3776    \n", "63.6643      66.0706     \n", "4.6755       4.6360      \n", "24.6287      25.8270     \n", "4.7523       4.9592      \n", "16.9264      17.0151     \n", "10.4276      10.4580     \n", "14.6770      14.9370     \n", "35.3582      35.3062     \n", "73.1112      71.4267     \n", "38.5363      36.4336     \n", "185.2238     186.3747    \n", "3.8961       4.7635      \n", "47.6921      47.5183     \n", "4.5228       3.5413      \n", "6.6865       8.2209      \n", "39.8646      40.1094     \n", "1240.3209    1308.5800   \n", "16.1234      15.7825     \n", "16.2129      16.6227     \n", "8.7242       8.5141      \n", "107.5382     110.1063    \n", "9.1563       8.7569      \n", "50.6455      51.1056     \n", "8.2780       8.3630      \n", "7.4420       8.4417      \n", "4.0223       4.5119      \n", "16.9201      16.8841     \n", "74.2135      73.5097     \n", "17.4796      17.8776     \n", "12.9635      12.0946     \n", "508.4443     479.4382    \n", "99.1364      99.9268     \n", "280.6788     279.8317    \n", "45.0934      44.1457     \n", "7.4074       6.7554      \n", "18.7718      18.6009     \n", "11.1229      11.1111     \n", "6.7776       8.4407      \n", "10.5762      10.5569     \n", "124.3417     119.6383    \n", "10.5415      10.6857     \n", "35.7924      35.6104     \n", "6.4854       6.4479      \n"]}], "source": ["# 使用随机森林模型进行回归预测\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.model_selection import train_test_split, cross_val_score, KFold\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score\n", "\n", "# 目标列\n", "targets = ['inference_time_ms', 'training_time_ms']\n", "df = df.dropna(subset=[c for c in targets if c in df.columns])\n", "\n", "# 数值特征列（排除目标与明显泄漏列）\n", "num_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "leak_cols = ['inference_time_ms', 'training_time_ms', 'memory_allocated_mb', 'memory_reserved_mb']\n", "X_cols = [c for c in num_cols if c not in leak_cols]\n", "X = df[X_cols].values\n", "predictor = {}\n", "for y_col in targets:\n", "    if y_col not in df.columns:\n", "        continue\n", "    y = df[y_col].values\n", "\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=0.2, random_state=42\n", "    )\n", "\n", "    # 使用网格搜索进行优化\n", "    from sklearn.model_selection import GridSearchCV\n", "\n", "    param_grid = {\n", "        'n_estimators': [100, 200, 400],\n", "        'max_depth': [None, 10, 20, 30],\n", "        'min_samples_split': [2, 5, 10],\n", "        'min_samples_leaf': [1, 2, 4]\n", "    }\n", "\n", "    base_model = RandomForestRegressor(\n", "        random_state=42,\n", "        n_jobs=-1\n", "    )\n", "\n", "    grid_search = GridSearchCV(\n", "        estimator=base_model,\n", "        param_grid=param_grid,\n", "        cv=3,\n", "        n_jobs=-1,\n", "        scoring='neg_root_mean_squared_error',\n", "        verbose=1\n", "    )\n", "    grid_search.fit(X_train, y_train)\n", "    print(f\"最佳参数: {grid_search.best_params_}\")\n", "    predictor[y_col] = grid_search.best_params_\n", "    model = grid_search.best_estimator_\n", "    model.fit(X_train, y_train)\n", "    y_pred = model.predict(X_test)\n", "\n", "    mse = mean_squared_error(y_test, y_pred)\n", "    rmse = mse ** 0.5\n", "    mae = mean_absolute_error(y_test, y_pred)\n", "    r2 = r2_score(y_test, y_pred)\n", "\n", "    print(f\"\\n目标: {y_col}\")\n", "    print(f\"MSE:  {mse:.4f}\")\n", "    print(f\"RMSE: {rmse:.4f}\")\n", "    print(f\"MAE:  {mae:.4f}\")\n", "    print(f\"R^2:  {r2:.4f}\")\n", "\n", "    # 5 折交叉验证（R^2 与 RMSE）\n", "    kf = KFold(n_splits=5, shuffle=True, random_state=42)\n", "    cv_r2 = cross_val_score(model, X, y, cv=kf, scoring=\"r2\", n_jobs=-1)\n", "    cv_rmse = -cross_val_score(model, X, y, cv=kf, scoring=\"neg_root_mean_squared_error\", n_jobs=-1)\n", "\n", "    print(f\"CV R^2:  mean {cv_r2.mean():.4f} ± {cv_r2.std():.4f}\")\n", "    print(f\"CV RMSE: mean {cv_rmse.mean():.4f} ± {cv_rmse.std():.4f}\")\n", "\n", "    # 特征重要性（Top 15）\n", "    importances = pd.Series(model.feature_importances_, index=X_cols).sort_values(ascending=False)\n", "    print(\"\\n重要特征(Top 15):\")\n", "    print(importances.head(15))\n", "\n", "\n", "    best_params = predictor[y_col]\n", "    print(\"预测结果 vs 真实结果（前100组）：\")\n", "    print(\"{:<12} {:<12}\".format(\"预测值\", \"真实值\"))\n", "    for pred, true in list(zip(y_pred, y_test))[:100]:\n", "        print(\"{:<12.4f} {:<12.4f}\".format(pred, true))\n", "\n", "    \n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}