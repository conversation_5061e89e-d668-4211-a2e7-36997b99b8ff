#!/bin/bash
# quick_run.sh - 一键运行 scaleDL 最小示例流程

set -e  # 出错就退出
PROJECT_DIR=$(dirname "$0")
cd "$PROJECT_DIR"

echo "===== Step 1: 数据采集 (SimpleModel 推理) ====="
python main.py collect \
  --model simple \
  --pattern inference \
  --out data/simple/collect/inference_demo.csv

echo "===== Step 2: 训练预测器 ====="
python main.py train \
  --model simple \
  --pattern inference \
  --csv data/simple/collect/inference_demo.csv \
  --target inference_time_ms \
  --predictor random_forest \
  --out_dir data/simple/predict/inference_time_ms_demo

echo "===== Step 3: 性能预测 ====="
python main.py predict \
  --predictor random_forest \
  --model_file data/simple/predict/inference_time_ms_demo/random_forest.joblib \
  --params batch_size=8 seq_len=128 hidden_size=256 num_layers=2 num_heads=4 dropout=0.1 \
           total_layers=20 total_params=1234567 trainable_params=1234567 \
           layer_Linear=8 layer_Dropout=4 layer_LayerNorm=2

echo "===== 全流程完成 ====="
