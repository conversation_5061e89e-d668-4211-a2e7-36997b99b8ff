"""性能预测器：随机森林回归（类封装 + 向下兼容函数接口）。"""
import os, joblib
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

LEAK_COLS = ['inference_time_ms', 'training_time_ms', 'memory_allocated_mb', 'memory_reserved_mb']

class RandomForestPredictor:
    def __init__(self, model=None, X_cols=None):
        self.model = model
        self.X_cols = X_cols

    def fit(self, df: pd.DataFrame, target: str):
        """训练模型并返回(y_pred, y_test, metrics)。"""
        if target not in df.columns:
            raise ValueError(f"目标列不存在: {target}. 可选: {list(df.columns)}")
        df = df.dropna(subset=[target])
        num_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        self.X_cols = [c for c in num_cols if c not in LEAK_COLS]
        X = df[self.X_cols].values
        y = df[target].values

        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        self.model = RandomForestRegressor(random_state=42, n_jobs=-1)
        self.model.fit(X_train, y_train)
        y_pred = self.model.predict(X_test)

        metrics = {
            'mse': float(mean_squared_error(y_test, y_pred)),
            'rmse': float(mean_squared_error(y_test, y_pred) ** 0.5),
            'mae': float(mean_absolute_error(y_test, y_pred)),
            'r2': float(r2_score(y_test, y_pred)),
        }
        return y_pred, y_test, metrics

    def predict(self, params_row: dict) -> float:
        """对单条参数配置进行预测。"""
        if self.model is None or self.X_cols is None:
            raise RuntimeError("模型未加载或未训练。请先调用 fit() 或 load()。")
        x = np.array([[params_row.get(col, 0) for col in self.X_cols]], dtype=float)
        return float(self.model.predict(x)[0])

    def save(self, path: str):
        """保存模型与特征列。"""
        if self.model is None or self.X_cols is None:
            raise RuntimeError("无可保存的模型/特征列。请先训练或加载。")
        joblib.dump({'model': self.model, 'X_cols': self.X_cols}, path)

    @classmethod
    def load(cls, path: str):
        """加载已保存的模型文件，返回实例。"""
        if not os.path.isfile(path):
            raise FileNotFoundError(f"[RandomForestPredictor] Error:load 模型文件不存在: {path}")
        try:
            obj = joblib.load(path)
        except Exception as e:
            raise RuntimeError(f"加载模型失败: {e}") from e

        if not isinstance(obj, dict) or 'model' not in obj or 'X_cols' not in obj:
            raise ValueError("[RandomForestPredictor] Error:load 无效的模型文件：缺少 'model' 或 'X_cols'")

        model, X_cols = obj['model'], obj['X_cols']
        if not hasattr(model, 'predict'):
            raise TypeError("[RandomForestPredictor] Error:load 加载对象不是可预测的模型（缺少 predict 方法）")
        if not isinstance(X_cols, (list, tuple)) or not all(isinstance(c, str) for c in X_cols):
            raise TypeError("[RandomForestPredictor] Error:load X_cols 格式不正确，应为字符串列表")

        return cls(model=model, X_cols=list(X_cols))
