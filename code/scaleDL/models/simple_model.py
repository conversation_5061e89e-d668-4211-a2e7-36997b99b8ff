"""一个用于性能基准的简化 Transformer 编码器模型。"""
import torch
import torch.nn as nn

class SimpleModel(nn.Module):
    """简化模型：Linear embedding -> TransformerEncoder -> AvgPool -> Linear classifier"""
    def __init__(self, input_size=100, seq_len=10, hidden_size=64, num_layers=2, num_heads=4, dropout=0.1, num_classes=10):
        super(SimpleModel, self).__init__()
        self.input_size = input_size
        self.seq_len = seq_len
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.num_heads = num_heads
        self.dropout = dropout
        self.num_classes = num_classes

        self.embedding = nn.Linear(input_size, hidden_size)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size,
            nhead=num_heads,
            dim_feedforward=hidden_size * 2,
            dropout=dropout,
            activation='relu'
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.dropout_layer = nn.Dropout(dropout)
        self.classifier = nn.Linear(hidden_size, num_classes)

    def forward(self, x):
        """前向：输入(batch, seq_len, input_size)，输出(batch, num_classes)。"""
        x = self.embedding(x)
        x = x.permute(1, 0, 2)
        x = self.transformer_encoder(x)
        x = x.mean(dim=0)
        x = self.dropout_layer(x)
        x = self.classifier(x)
        return x