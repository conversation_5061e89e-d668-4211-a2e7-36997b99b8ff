"""一个用于性能基准/微调的 BERT 模型封装。"""
import torch.nn as nn
from transformers import BertConfig, BertForSequenceClassification

# 将 seq_len 暴露为构造参数，并用于随机输入长度
class BERT(nn.Module):
    def __init__(self, vocab_size=30522, hidden_size=768, num_hidden_layers=12,
                 num_attention_heads=12, intermediate_size=3072, hidden_dropout_prob=0.1,
                 num_labels=2, seq_len=128):
        super().__init__()
        config = BertConfig(
            vocab_size=vocab_size,
            hidden_size=hidden_size,
            num_hidden_layers=num_hidden_layers,
            num_attention_heads=num_attention_heads,
            intermediate_size=intermediate_size,
            hidden_dropout_prob=hidden_dropout_prob,
            num_labels=num_labels,
        )
        self.bert = BertForSequenceClassification(config)
        self.vocab_size = vocab_size
        self.hidden_size = hidden_size
        self.num_hidden_layers = num_hidden_layers
        self.num_attention_heads = num_attention_heads
        self.intermediate_size = intermediate_size
        self.hidden_dropout_prob = hidden_dropout_prob
        self.num_labels = num_labels
        self.seq_len = seq_len            
        self.num_classes = num_labels

    def forward(self, input_ids=None, attention_mask=None, labels=None):
        out = self.bert(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
        return out.logits

    @classmethod
    def from_pretrained(cls, model_name_or_path, num_labels=2, seq_len=128, **kw):
        config = BertConfig.from_pretrained(model_name_or_path, num_labels=num_labels, **kw)
        obj = cls(vocab_size=config.vocab_size, hidden_size=config.hidden_size,
                  num_hidden_layers=config.num_hidden_layers, num_attention_heads=config.num_attention_heads,
                  intermediate_size=config.intermediate_size, hidden_dropout_prob=config.hidden_dropout_prob,
                  num_labels=config.num_labels, seq_len=seq_len)
        obj.bert = BertForSequenceClassification.from_pretrained(model_name_or_path, config=config)
        return obj