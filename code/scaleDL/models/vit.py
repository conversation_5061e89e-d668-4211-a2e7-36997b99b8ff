"""一个用于性能基准的 ViT (Vision Transformer) 模型。"""
import torch
import torch.nn as nn
from transformers import ViTModel, ViTConfig

class ViT(nn.Module):
    """ViT模型：基于 HuggingFace transformers 的 Vision Transformer"""
    def __init__(self, image_size=224, patch_size=16, num_channels=3, hidden_size=768, 
                 num_hidden_layers=12, num_attention_heads=12, intermediate_size=3072, 
                 hidden_dropout_prob=0.0, attention_probs_dropout_prob=0.0, 
                 initializer_range=0.02, layer_norm_eps=1e-12, num_classes=10):
        super(ViT, self).__init__()
        self.image_size = image_size
        self.patch_size = patch_size
        self.num_channels = num_channels
        self.hidden_size = hidden_size
        self.num_hidden_layers = num_hidden_layers
        self.num_attention_heads = num_attention_heads
        self.intermediate_size = intermediate_size
        self.hidden_dropout_prob = hidden_dropout_prob
        self.attention_probs_dropout_prob = attention_probs_dropout_prob
        self.initializer_range = initializer_range
        self.layer_norm_eps = layer_norm_eps
        self.num_classes = num_classes

        # 创建 ViT 配置
        config = ViTConfig(
            image_size=image_size,
            patch_size=patch_size,
            num_channels=num_channels,
            hidden_size=hidden_size,
            num_hidden_layers=num_hidden_layers,
            num_attention_heads=num_attention_heads,
            intermediate_size=intermediate_size,
            hidden_dropout_prob=hidden_dropout_prob,
            attention_probs_dropout_prob=attention_probs_dropout_prob,
            initializer_range=initializer_range,
            layer_norm_eps=layer_norm_eps,
        )
        
        # 初始化 ViT 主干网络
        self.vit = ViTModel(config)
        
        # 分类头
        self.classifier = nn.Linear(hidden_size, num_classes)

    def forward(self, x):
        """前向：输入(batch, num_channels, image_size, image_size)，输出(batch, num_classes)。"""
        # ViT 输出包含 last_hidden_state 等，我们取 [CLS] token 用于分类
        outputs = self.vit(x)
        # 取 [CLS] token 的输出 (batch_size, hidden_size)
        cls_output = outputs.last_hidden_state[:, 0, :]
        # 通过分类头
        logits = self.classifier(cls_output)
        return logits

    @classmethod
    def from_pretrained(cls, model_name_or_path, num_classes=None, **kwargs):
        """从预训练模型加载，可选修改分类头类别数"""
        # 加载预训练配置
        config = ViTConfig.from_pretrained(model_name_or_path)
        
        # 更新配置参数
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        # 创建模型实例
        model = cls(
            image_size=config.image_size,
            patch_size=config.patch_size,
            num_channels=config.num_channels,
            hidden_size=config.hidden_size,
            num_hidden_layers=config.num_hidden_layers,
            num_attention_heads=config.num_attention_heads,
            intermediate_size=config.intermediate_size,
            hidden_dropout_prob=config.hidden_dropout_prob,
            attention_probs_dropout_prob=config.attention_probs_dropout_prob,
            initializer_range=config.initializer_range,
            layer_norm_eps=config.layer_norm_eps,
            num_classes=num_classes or config.num_labels or 1000,
        )
        
        # 加载预训练权重（除了分类头）
        pretrained_model = ViTModel.from_pretrained(model_name_or_path, config=config)
        model.vit.load_state_dict(pretrained_model.state_dict())
        
        return model