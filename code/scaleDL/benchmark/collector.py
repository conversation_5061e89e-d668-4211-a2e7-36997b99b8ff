"""采集基准数据：遍历超参数组合，记录推理/训练性能与模型结构特征。"""
import itertools
import pandas as pd
import torch
from torch.cuda import device_count
from tqdm import tqdm
from models.simple_model import SimpleModel
from models.vit import ViT
from models.bert import BERT
from features.extractor import ModelFeatureExtractor
from benchmark.inference import benchmark_inference
from benchmark.training import benchmark_training

def _build_model(model_cls, params, device):
    """根据超参数构建模型并放到指定 device。"""
    if model_cls == SimpleModel:
        return model_cls(
            input_size=int(params['hidden_size']),
            seq_len=int(params['seq_len']),
            hidden_size=int(params['hidden_size']),
            num_layers=int(params['num_layers']),
            num_heads=int(params['num_heads']),
            dropout=float(params['dropout']),
            num_classes=10,
        ).to(device)
    elif model_cls == ViT:
        return model_cls(
            image_size=int(params['image_size']),
            patch_size=int(params['patch_size']),
            hidden_size=int(params['hidden_size']),
            num_hidden_layers=int(params['num_layers']),
            num_attention_heads=int(params['num_heads']),
            hidden_dropout_prob=float(params['dropout']),
            attention_probs_dropout_prob=float(params['dropout']),
            num_classes=10,
        ).to(device)
    elif model_cls == BERT:
        return model_cls(
            hidden_size=int(params['hidden_size']),
            num_hidden_layers=int(params['num_layers']),
            num_attention_heads=int(params['num_heads']),
            intermediate_size=int(params.get('intermediate_size', int(params['hidden_size']) * 4)),
            hidden_dropout_prob=float(params['dropout']),
            num_labels=2,
            seq_len=int(params['seq_len']),
        ).to(device)
    else:
        raise ValueError(f"[Collector] build model 发现不支持的模型类型: {model_cls}")

def _feature_row(model, params):
    """抽取模型结构特征并与超参数合并为一行记录。"""
    extractor = ModelFeatureExtractor()
    features, layer_counts, total_params, trainable_params = extractor.extract_features(model)
    row = {
        **params,
        'total_layers': features[0],
        'total_params': total_params,
        'trainable_params': trainable_params,
    }
    for k, v in layer_counts.items():
        row[f'layer_{k}'] = v
    return row

def collect_inference_data(model_cls, hyperparameter_ranges, device, num_iter=5):
    """采集推理基准数据。
    
    参数:
        model_cls: 模型类（如 SimpleModel）
        hyperparameter_ranges: 超参数搜索空间字典
        device: 运行设备（torch.device）
        num_iter: 推理计时迭代次数
    返回:
        DataFrame: 每组配置的推理耗时/显存与结构特征
    """
    results, names, values = [], list(hyperparameter_ranges.keys()), list(hyperparameter_ranges.values())
    pbar = tqdm(total=__import__('numpy').prod([len(v) for v in values]), desc="测试进度")
    for combo in itertools.product(*values):
        params = dict(zip(names, combo))
        try:
            model = _build_model(model_cls, params, device)
            row = _feature_row(model, params)
            inf_ms, mem_alloc, mem_resv = benchmark_inference(
                model, params['batch_size'], params['seq_len'], params['hidden_size'], num_iterations=num_iter
            )
            row.update({
                'inference_time_ms': inf_ms,
                'memory_allocated_mb': mem_alloc,
                'memory_reserved_mb': mem_resv,
            })
            results.append(row)
        except Exception as e:
            print(f"测试参数 {params} 时出错: {e}")
        finally:
            del model
            if torch.cuda.is_available(): torch.cuda.empty_cache()
            pbar.update(1)
    pbar.close()
    return pd.DataFrame(results)

def collect_training_data(model_cls, hyperparameter_ranges, device, num_epochs=5):
    """采集训练基准数据。
    
    参数:
        model_cls: 模型类（如 SimpleModel）
        hyperparameter_ranges: 超参数搜索空间字典
        device: 运行设备（torch.device）
        num_epochs: 训练计时循环次数
    返回:
        DataFrame: 每组配置的训练耗时与结构特征
    """
    results, names, values = [], list(hyperparameter_ranges.keys()), list(hyperparameter_ranges.values())
    pbar = tqdm(total=__import__('numpy').prod([len(v) for v in values]), desc="测试进度")
    for combo in itertools.product(*values):
        params = dict(zip(names, combo))
        try:
            model = _build_model(model_cls, params, device)
            # print(f"[Collector] collect training data 模型参数: {params}")
            row = _feature_row(model, params)
            train_ms = benchmark_training(
                model, params['batch_size'], num_epochs=num_epochs
            )
            row.update({'training_time_ms': train_ms})
            results.append(row)
        except Exception as e:
            print(f"测试参数 {params} 时出错: {e}")
        finally:
            del model
            if torch.cuda.is_available(): torch.cuda.empty_cache()
            pbar.update(1)
    pbar.close()
    return pd.DataFrame(results)