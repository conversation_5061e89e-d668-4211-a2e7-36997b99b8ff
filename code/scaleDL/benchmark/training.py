"""基准-训练：评测单步训练（前向+反向+更新）耗时。"""
import time
import torch
import torch.nn as nn
import torch.optim as optim

def _generate_input_data(model, batch_size, device):
    """根据模型类型生成对应的输入数据。
    
    参数:
        model: 待测模型
        batch_size: 批次大小
        device: 运行设备
    返回:
        x: 输入数据张量
        y: 标签张量
    """
    model_name = model.__class__.__name__
    
    if model_name == 'SimpleModel':
        # SimpleModel: (batch_size, seq_len, hidden_size)
        seq_len = model.seq_len
        hidden_size = model.hidden_size
        x = torch.randn(batch_size, seq_len, hidden_size, device=device)
        y = torch.randint(0, model.num_classes, (batch_size,), device=device)
        
    elif model_name == 'ViT':
        # ViT: (batch_size, num_channels, image_size, image_size)
        num_channels = model.num_channels
        image_size = model.image_size
        x = torch.randn(batch_size, num_channels, image_size, image_size, device=device)
        y = torch.randint(0, model.num_classes, (batch_size,), device=device)
        
    elif model_name == 'BERT':
        # BERT: (batch_size, seq_len)
        seq_len = model.seq_len
        x = torch.randint(0, model.vocab_size, (batch_size, seq_len), device=device)
        y = torch.randint(0, model.num_classes, (batch_size,), device=device)
        
    else:
        raise ValueError(f"[Training] Error: 不支持的模型类型: {model_name}")
    
    return x, y

def benchmark_training(model, batch_size, num_epochs=5, lr=1e-3):
    """评测模型训练性能（单步训练耗时）。
    
    参数:
        model: 待测模型
        batch_size: 批次大小
        num_epochs: 计时循环次数（每次包含前向+反向+优化）
        lr: 学习率
    返回:
        avg_training_time_ms: 平均每个 epoch 的训练耗时（毫秒）
    """
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.train().to(device)

    # 根据模型类型生成输入数据
    x, y = _generate_input_data(model, batch_size, device)
    
    crit = nn.CrossEntropyLoss()
    opt = optim.Adam(model.parameters(), lr=lr)

    # 预热
    for _ in range(3):
        opt.zero_grad()
        loss = crit(model(x), y)
        loss.backward()
        opt.step()

    if torch.cuda.is_available():
        torch.cuda.synchronize()
    
    # 计时训练
    t0 = time.time()
    for _ in range(num_epochs):
        opt.zero_grad()
        loss = crit(model(x), y)
        loss.backward()
        opt.step()
    
    if torch.cuda.is_available():
        torch.cuda.synchronize()

    return (time.time() - t0) / num_epochs * 1000