"""基准-推理：只评测前向推理耗时与显存占用。"""
import time
import torch

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

@torch.no_grad()
def benchmark_inference(model, batch_size, seq_len, hidden_size, num_iterations=5, warmup=3):
    """评测模型推理性能（前向计算）。
    
    参数:
        model: 待测模型（已构建好的 nn.Module）
        batch_size, seq_len, hidden_size: 生成随机输入的形状
        TODO：之后应该使用数据集进行输入
        num_iterations: 计时的迭代次数
        warmup: 预热次数（不计入时间）
    返回:
        (avg_inference_time_ms, memory_allocated_mb, memory_reserved_mb)
    """
    model.eval().to(device)
    x = torch.randn(batch_size, seq_len, hidden_size, device=device)

    for _ in range(warmup):
        _ = model(x)
    if torch.cuda.is_available():
        torch.cuda.synchronize()

    t0 = time.time()
    for _ in range(num_iterations):
        _ = model(x)
    if torch.cuda.is_available():
        torch.cuda.synchronize()
    avg_ms = (time.time() - t0) / num_iterations * 1000

    mem_alloc = torch.cuda.memory_allocated() / 1024 / 1024 if torch.cuda.is_available() else 0.0
    mem_resv  = torch.cuda.memory_reserved()  / 1024 / 1024 if torch.cuda.is_available() else 0.0
    return avg_ms, mem_alloc, mem_resv