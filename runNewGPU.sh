#!/usr/bin/env bash

# 新GPU数据收集和性能预测脚本
#
# 该脚本用于Case 2: 在新GPU上收集数据并建立预测模型
#
# 功能：
# 1. 收集PyTorch和HuggingFace模型在新GPU上的性能数据
# 2. 计算FLOPs和实际执行时间
# 3. 建立内核级回归模型
# 4. 验证模型预测准确性
#
# 环境要求：
# - Ubuntu 20.04.5 LTS
# - CUDA 11.6
# - cuDNN 8.3.2
# - 已运行installNewGPU.sh安装依赖
#
# 数据收集流程：
# 1. PyTorch模型：计算FLOPs -> 性能测试 -> 数据处理 -> 内核映射
# 2. HuggingFace模型：同样的四步流程
# 3. 回归建模：为每种内核建立预测模型
# 4. 模型验证：计算预测误差
#
# 输出文件：
# - bs128dataprocesswithops_regression_hug.csv: HuggingFace模型回归数据
# - bs128dataprocesswithops_regression_torch.csv: PyTorch模型回归数据
#
# 使用方法：
# chmod +x runNewGPU.sh
# ./runNewGPU.sh

echo "开始在新GPU上收集性能数据..."

echo "=== 第一阶段：PyTorch模型数据收集 ==="
cd code
cd torch

echo "步骤1: 计算PyTorch模型FLOPs (批大小=128)..."
#128 means BS=128
python flopsmodels.py 128 > bs128flops.csv

echo "步骤2: 运行PyTorch模型性能测试..."
python runmodels.py 128

echo "步骤3: 处理PyTorch模型trace数据..."
python dataprocess.py > bs128dataprocess.csv

echo "步骤4: 映射PyTorch内核与操作..."
python mapkernelops.py bs128dataprocess.csv bs128flops.csv > bs128dataprocesswithops.csv

echo "=== 第二阶段：HuggingFace模型数据收集 ==="
cd ../huggingface

echo "步骤5: 计算HuggingFace模型FLOPs..."
chmod +x runflops.sh
./runflops.sh

echo "步骤6: 运行HuggingFace模型性能测试..."
chmod +x runmodel.sh
./runmodel.sh

echo "步骤7: 处理HuggingFace模型trace数据..."
python dataprocess.py > bs128dataprocess.csv

echo "步骤8: 映射HuggingFace内核与操作..."
python mapkernelops.py bs128dataprocess.csv bsflops.csv > bs128dataprocesswithops.csv

echo "=== 第三阶段：回归建模 ==="
echo "步骤9: 建立HuggingFace模型回归模型..."
python regressionfunction.py ./bs128dataprocesswithops.csv > bs128dataprocesswithops_regression_hug.csv

echo "步骤10: 建立PyTorch模型回归模型..."
python regressionfunction.py ../torch/bs128dataprocesswithops.csv > bs128dataprocesswithops_regression_torch.csv

echo "=== 第四阶段：模型验证 ==="
echo "步骤11: 计算所有网络的平均误差率..."
#mean error rate for all the networks in the test set.
python regressionresult.py ./bs128dataprocesswithops_regression_hug.csv ./bs128dataprocesswithops_regression_torch.csv

echo "步骤12: 计算ResNet-50的误差率..."
#error rate for ResNet-50.
python regressionresult.py ./bs128dataprocesswithops_regression_hug.csv ./bs128dataprocesswithops_regression_torch.csv resnet50

echo "新GPU数据收集和建模完成！"
