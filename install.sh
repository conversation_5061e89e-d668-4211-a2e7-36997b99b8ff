#!/usr/bin/env bash

# 深度神经网络执行时间预测项目 - 依赖安装脚本
#
# 该脚本用于安装Case 1所需的Python依赖包
# 适用于基于已有GPU数据集进行预测分析的场景
#
# 安装的包：
# - pandas: 数据处理和分析
# - scikit-learn: 机器学习库（线性回归等）
# - matplotlib: 基础绘图库
# - statsmodels: 统计建模库
# - seaborn: 高级数据可视化库
#
# 使用方法：
# chmod +x install.sh
# ./install.sh
#
# 注意：
# - 确保已安装Python 3.6+
# - 建议在虚拟环境中运行
# - 如需GPU支持，请单独安装PyTorch和CUDA

echo "开始安装深度神经网络性能预测项目依赖..."

echo "安装数据处理库..."
pip install pandas

echo "安装机器学习库..."
pip install -U scikit-learn

echo "安装绘图库..."
pip install matplotlib

echo "安装统计建模库..."
pip install statsmodels

echo "安装数据可视化库..."
pip install -U seaborn

echo "依赖安装完成！"
echo "现在可以运行 ./run.sh 开始性能分析"

