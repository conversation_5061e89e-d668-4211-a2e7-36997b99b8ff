import torch
import torch.nn as nn
import torch.nn.functional as F

# 定义一个简单的 Conv + BN + ReLU 模型
class SmallNet(nn.Module):
    def __init__(self):
        super().__init__()
        self.conv = nn.Conv2d(3, 16, 3, stride=1, padding=1, bias=True)
        self.bn = nn.BatchNorm2d(16)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        return self.relu(self.bn(self.conv(x)))

# 转 TorchScript
model = SmallNet().eval()
scripted = torch.jit.script(model)

# 打印原始 graph（未优化）
print("=== 原始 Graph ===")
print(scripted.graph)

# 用 graph_for 打印优化后的 graph
dummy_input = torch.randn(1, 3, 224, 224)
print("\n=== 优化后 Graph ===")
print(scripted.graph_for(dummy_input))

# 实际运行一次，确保走优化后的 graph
out = scripted(dummy_input)
print("\n输出 shape:", out.shape)
