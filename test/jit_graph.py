import torch
import torch.nn as nn

m = nn.Sequential(
    nn.Conv2d(3, 16, 3, stride=1, padding=1),
    nn.BatchNorm2d(16),
    nn.ReLU()
)

example = torch.randn(1, 3, 224, 224)

# 转成 TorchScript
traced = torch.jit.trace(m, example)

# 打印 graph（融合前）
print("=== 原始 graph ===")
print(traced.graph)

# 优化（融合）
traced_optimized = torch.jit.optimize_for_inference(traced)

print("=== 融合后 graph ===")
print(traced_optimized.graph)
